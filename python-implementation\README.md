# AL Planner - Python Implementation

This is a Python implementation of the AL Planner autonomous path planning application, originally written in Flutter/Dart with Rust backend. It provides a complete GUI application for designing and simulating robot autonomous paths using interactive Bezier curves.

## Features

- **Interactive Path Editor**: Draw and edit Bezier curve paths with mouse interactions
- **Motion Profiling**: Real-time velocity and acceleration constraint calculations
- **Robot Simulation**: Visualize robot movement along paths with live position updates
- **Command Timeline**: Add timed commands that execute during path following
- **Velocity Charts**: Real-time linear and angular velocity visualization
- **Field Backgrounds**: Skills and match field images for context
- **JSON Project Files**: Save and load path configurations
- **Live Robot Data**: Optional streaming from actual robot hardware

## Requirements

### Required
- Python 3.8 or higher
- tkinter (usually included with Python)

### Optional (for enhanced features)
- matplotlib (for velocity charts)
- numpy (for mathematical operations)
- Pillow/PIL (for background images)

## Installation

1. **Clone or download this repository**

2. **Install optional dependencies** (recommended):
```bash
pip install -r requirements.txt
```

Or install individually:
```bash
pip install matplotlib numpy Pillow
```

3. **Test the installation**:
```bash
python test_app.py
```

## Quick Start

1. **Run the application**:
```bash
python main.py
```

2. **Open a project**:
   - Click "File" → "Open Project" or press Ctrl+O
   - Navigate to the `sample_project` folder and select it
   - The example path will load automatically

3. **Edit the path**:
   - Double-click on the canvas to add new path segments
   - Drag control points to modify the path shape
   - Hold Shift and click/drag to delete segments

4. **Adjust constraints**:
   - Use the velocity controls on the right to set speed and acceleration limits
   - Toggle path segment properties (reversed, stop end, visibility)

5. **Add commands**:
   - Click "Add Command" to create timed actions
   - Drag the time slider to position commands along the path
   - Edit command names for your specific actions

6. **Simulate the path**:
   - Click "Play" to animate the robot following the path
   - View real-time velocity charts
   - Toggle "Live Robot" for hardware integration

## Project Structure

```
python-implementation/
├── main.py                 # Application entry point
├── test_app.py            # Test suite
├── requirements.txt       # Python dependencies
├── config/
│   └── constants.py       # Application constants
├── utils/                 # Core utility classes
│   ├── point.py          # 2D point operations
│   ├── bezier.py         # Bezier curve mathematics
│   ├── robot.py          # Robot position handling
│   └── command.py        # Command system
├── motion_profiling/      # Path optimization
│   ├── motion_profile.py # Motion profile generation
│   ├── path_segment.py   # Path segment handling
│   └── simple_api.py     # Simplified API interface
├── gui/                   # User interface components
│   ├── main_window.py    # Main application window
│   ├── home_screen.py    # File browser and layout
│   ├── pathing_screen.py # Main editing interface
│   ├── path_canvas.py    # Interactive path editor
│   ├── velocity_controls.py # Speed/acceleration controls
│   ├── command_editor.py # Command timeline editor
│   ├── chart_display.py  # Velocity visualization
│   └── robot_simulation.py # Robot data streaming
├── assets/               # Field images and resources
└── sample_project/       # Example project files
    └── example_path.json
```

## Controls and Shortcuts

### Mouse Controls
- **Double-click**: Add new Bezier curve segment at cursor position
- **Ctrl + Double-click**: Add new straight line segment at cursor position
- **Left-click + Drag**: Move control points to reshape curves
- **Shift + Click/Drag**: Delete curve segments under cursor
- **Mouse wheel**: Scroll through interface sections

### Control Point Colors
Each Bezier curve has 4 control points with distinct visual styles:

- **🟢 Start Point**: Large green circle (filled) - where the robot begins this segment
- **🔷 Control Point 1**: Blue square - controls the curve direction from start point
- **🔶 Control Point 2**: Purple diamond - controls the curve direction to end point
- **🔴 End Point**: Circle - red (filled) if stop_end=true, orange if continuing

Light gray dashed lines connect the control points to show their relationships.

### Straight Lines vs Curves
- **🔵 Curved segments**: Blue solid lines with all 4 control points visible
- **🟣 Straight segments**: Magenta dashed lines with only start/end points visible
- **Toggle**: Use the "Line" checkbox in velocity controls to convert between straight and curved
- **Creation**: Hold Ctrl while double-clicking to create straight line segments directly

### Deleting Path Segments
There are multiple ways to delete path segments:

1. **Shift + Click**: Hold Shift and click on any control point of the segment to delete
2. **Shift + Drag**: Hold Shift and drag over segments to delete multiple
3. **Delete/Backspace Key**: Press Delete or Backspace to remove the last segment
4. **Visibility Toggle**: Use the eye icon in the velocity controls to hide segments

### Keyboard Shortcuts
- **Ctrl+O**: Open project directory
- **Ctrl+S**: Save current file
- **Ctrl+Shift+S**: Save as new file
- **Ctrl+K**: Switch to next file in project
- **Ctrl+I**: Switch to previous file in project

### Interface Controls
- **Skills Mode**: Toggle between match and skills field backgrounds
- **Start/End Speed**: Set initial and final velocities for the path
- **Play/Pause**: Animate robot following the path
- **Flip Path**: Mirror the entire path across the x-axis
- **Live Robot**: Enable real-time robot position streaming

## File Format

Projects are saved as JSON files with the following structure:

```json
{
  "start_speed": 0,
  "end_speed": 0,
  "segments": [
    {
      "inverted": false,
      "stop_end": false,
      "path": [
        {"x": 0.5, "y": 0.5},
        {"x": 0.8, "y": 0.5},
        {"x": 1.2, "y": 0.8},
        {"x": 1.5, "y": 1.0}
      ],
      "constraints": {
        "velocity": 1.0,
        "accel": 2.0
      }
    }
  ],
  "commands": [
    {
      "t": 0.5,
      "name": "start_intake"
    }
  ]
}
```

### Field Coordinate System
- Origin (0,0) is at the bottom-left corner
- Field dimensions: 3.65m × 3.65m
- Coordinates are in meters
- Velocities in m/s, accelerations in m/s²

## Live Robot Integration

The application can connect to live robot hardware for real-time position visualization:

1. **Enable Live Robot**: Check the "Live Robot" option
2. **Robot Data Format**: The robot should send JSON data via HTTP:
   ```json
   [[x, y, angle], [x2, y2, angle2], ...]
   ```
3. **Default URL**: `http://***********/uart0`
4. **Fallback**: If connection fails, mock simulation will start automatically

## Troubleshooting

### Common Issues

1. **"PIL not available" warning**:
   - Install Pillow: `pip install Pillow`
   - Background images will be disabled but functionality remains

2. **"Matplotlib not available" warning**:
   - Install matplotlib: `pip install matplotlib`
   - Velocity charts will show text data instead of graphs

3. **Application won't start**:
   - Run the test suite: `python test_app.py`
   - Check Python version: `python --version` (requires 3.8+)
   - Verify tkinter: `python -c "import tkinter"`

4. **Path editing not working**:
   - Ensure you're double-clicking to add segments
   - Try different mouse positions on the canvas
   - Check that the canvas has focus (click on it first)

### Performance Tips

- Close unused project files to improve performance
- Limit the number of path segments for complex paths
- Reduce chart update frequency for smoother animation
- Use "Live Robot" sparingly to avoid network overhead

## Development

### Running Tests
```bash
python test_app.py
```

### Adding New Features
1. Core functionality goes in `utils/` or `motion_profiling/`
2. GUI components go in `gui/`
3. Configuration in `config/constants.py`
4. Follow the existing code structure and naming conventions

### Contributing
This is a faithful Python port of the original Flutter/Dart application. When making changes, consider compatibility with the original file format and user interface paradigms.

## License

This implementation maintains compatibility with the original AL Planner project. Please refer to the original project's license terms.
