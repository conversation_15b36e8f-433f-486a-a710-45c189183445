PODS:
  - FlutterMacOS (1.0.0)
  - rust_lib_al_planner (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - FlutterMacOS (from `Flutter/ephemeral`)
  - rust_lib_al_planner (from `Flutter/ephemeral/.symlinks/plugins/rust_lib_al_planner/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)

EXTERNAL SOURCES:
  FlutterMacOS:
    :path: Flutter/ephemeral
  rust_lib_al_planner:
    :path: Flutter/ephemeral/.symlinks/plugins/rust_lib_al_planner/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin

SPEC CHECKSUMS:
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  rust_lib_al_planner: 70f9f4a15212ee4ca7db686d569c5e9d9b6fda9f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.15.2
