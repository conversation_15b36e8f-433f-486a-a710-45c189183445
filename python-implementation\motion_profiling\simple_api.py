"""
Simple API for motion profiling - equivalent to the Rust API.
"""

import math
from typing import Optional
from utils.robot import Pose
from utils.bezier import Bezier
from .motion_profile import MotionProfile, Path
from .path_segment import PathSegment, Constraints
from config.constants import INCHES_TO_METERS, METERS_TO_INCHES


# Global motion profile instance and path data
_current_motion_profile: Optional[MotionProfile] = None
_current_beziers: list = []
_current_start_speed: float = 0.0
_current_end_speed: float = 0.0


def create_path_from_beziers(beziers: list, start_speed: float, end_speed: float) -> Path:
    """
    Create a Path object from a list of Bezier curves.

    Args:
        beziers: List of Bezier objects
        start_speed: Starting speed in m/s
        end_speed: Ending speed in m/s

    Returns:
        Path object
    """
    global _current_beziers, _current_start_speed, _current_end_speed

    # Store the beziers for direct path evaluation
    _current_beziers = beziers.copy()
    _current_start_speed = start_speed
    _current_end_speed = end_speed

    segments = []

    for bezier in beziers:
        # Convert Bezier to PathSegment
        path_points = [bezier.p1, bezier.p2, bezier.p3, bezier.p4]
        constraints = Constraints(
            velocity=bezier.path_max_speed * INCHES_TO_METERS,
            accel=bezier.path_max_accel * INCHES_TO_METERS
        )

        segment = PathSegment(
            inverted=bezier.reversed,
            stop_end=bezier.stop_end,
            path=path_points,
            constraints=constraints
        )
        segments.append(segment)

    return Path(start_speed, end_speed, segments)


def get_duration(path: Path) -> int:
    """
    Get the duration of a path in milliseconds.

    Args:
        path: Path object

    Returns:
        Duration in milliseconds
    """
    global _current_motion_profile, _current_beziers, _current_start_speed, _current_end_speed

    try:
        _current_motion_profile = MotionProfile(path)
        return _current_motion_profile.get_duration_ms()
    except Exception:
        # Return a large duration if motion profiling fails
        return 1000000000  # 1000 seconds


def get_pose(t: float) -> Pose:
    """
    Get the robot pose at time t by directly evaluating the Bezier path.

    Args:
        t: Time in seconds

    Returns:
        Pose object with x, y, theta
    """
    global _current_motion_profile, _current_beziers

    if _current_motion_profile is None or not _current_beziers:
        return Pose(0.0, 0.0, 0.0)

    # Get total duration
    total_duration = _current_motion_profile.get_duration_ms() / 1000.0
    if total_duration <= 0 or t < 0:
        return Pose(0.0, 0.0, 0.0)

    # Clamp time to valid range
    t = min(t, total_duration)

    # Calculate progress through the entire path (0 to len(beziers))
    progress = (t / total_duration) * len(_current_beziers)

    # Find which bezier segment we're on
    segment_index = int(progress)
    local_t = progress - segment_index

    # Handle edge cases
    if segment_index >= len(_current_beziers):
        segment_index = len(_current_beziers) - 1
        local_t = 1.0

    # Get the current bezier segment
    bezier = _current_beziers[segment_index]

    # Evaluate position on the bezier curve
    position = bezier.evaluate(local_t)

    # Calculate heading from the derivative (tangent)
    if local_t < 1.0:
        # Get tangent vector from derivative
        derivative = bezier.get_derivative(local_t)
        heading = math.atan2(derivative.y, derivative.x)
    else:
        # At the end, use the direction to the next segment or previous direction
        if segment_index < len(_current_beziers) - 1:
            next_bezier = _current_beziers[segment_index + 1]
            direction = next_bezier.p2.minus(next_bezier.p1)
            heading = math.atan2(direction.y, direction.x)
        else:
            # Last segment, use the final direction
            direction = bezier.p4.minus(bezier.p3)
            heading = math.atan2(direction.y, direction.x)

    # Adjust for reversed segments
    if bezier.reversed:
        heading += math.pi

    return Pose(position.x, position.y, heading)


def get_velocity(t: float) -> float:
    """
    Get the robot velocity at time t.
    
    Args:
        t: Time in seconds
        
    Returns:
        Velocity in inches per second
    """
    global _current_motion_profile
    
    if _current_motion_profile is None:
        return 0.0
    
    command = _current_motion_profile.get_command_at_time(int(t * 1000))
    if command is None:
        return 0.0
    
    # Convert from m/s to in/s
    return command.desired_velocity * METERS_TO_INCHES


def get_angular_velocity(t: float) -> float:
    """
    Get the robot angular velocity at time t.
    
    Args:
        t: Time in seconds
        
    Returns:
        Angular velocity in degrees per second
    """
    global _current_motion_profile
    
    if _current_motion_profile is None:
        return 0.0
    
    command = _current_motion_profile.get_command_at_time(int(t * 1000))
    if command is None:
        return 0.0
    
    # Convert from rad/s to deg/s
    import math
    return math.degrees(command.desired_angular)


def reset_motion_profile():
    """Reset the current motion profile."""
    global _current_motion_profile, _current_beziers, _current_start_speed, _current_end_speed
    _current_motion_profile = None
    _current_beziers = []
    _current_start_speed = 0.0
    _current_end_speed = 0.0
