#!/usr/bin/env python3
"""
Test script to verify the AL Planner application works correctly.
"""

import sys
import os
import json
import tempfile

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test core utilities
        from utils.point import Point
        from utils.bezier import Bezier
        from utils.robot import RobotPosition
        from utils.command import Command
        print("✓ Core utilities imported successfully")
        
        # Test motion profiling
        from motion_profiling.simple_api import get_duration, get_pose, get_velocity
        print("✓ Motion profiling imported successfully")
        
        # Test GUI components
        from gui.main_window import MainWindow
        from gui.path_canvas import PathCanvas
        from gui.velocity_controls import VelocityControls
        from gui.command_editor import CommandEditor
        from gui.chart_display import ChartDisplay
        print("✓ GUI components imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False


def test_core_classes():
    """Test core utility classes."""
    print("\nTesting core classes...")

    try:
        from utils.point import Point
        from utils.bezier import Bezier
        from utils.robot import RobotPosition
        from utils.command import Command

        # Test Point class
        p1 = Point(1.0, 2.0)
        p2 = Point(3.0, 4.0)
        p3 = p1.plus(p2)
        assert p3.x == 4.0 and p3.y == 6.0, "Point addition failed"
        
        # Test JSON serialization
        json_data = p1.to_json()
        p1_restored = Point.from_json(json_data)
        assert p1.x == p1_restored.x and p1.y == p1_restored.y, "Point JSON serialization failed"
        print("✓ Point class working correctly")
        
        # Test Bezier class
        bezier = Bezier(Point(0, 0), Point(1, 0), Point(2, 1), Point(3, 1))
        point_at_half = bezier.evaluate(0.5)
        assert isinstance(point_at_half, Point), "Bezier evaluation failed"
        
        # Test Bezier JSON
        bezier_json = bezier.to_json()
        bezier_restored = Bezier.from_json(bezier_json)
        assert bezier_restored.p1.x == bezier.p1.x, "Bezier JSON serialization failed"
        print("✓ Bezier class working correctly")
        
        # Test RobotPosition class
        robot = RobotPosition(1.0, 2.0, 0.5)
        screen_pos = robot.get_screen_position(800, 600)
        assert len(screen_pos) == 2, "Robot screen position failed"
        print("✓ RobotPosition class working correctly")
        
        # Test Command class
        cmd = Command(1.5, "test_command")
        cmd_json = cmd.to_json()
        cmd_restored = Command.from_json(cmd_json)
        assert cmd_restored.name == cmd.name, "Command JSON serialization failed"
        print("✓ Command class working correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Core class test failed: {e}")
        return False


def test_motion_profiling():
    """Test motion profiling functionality."""
    print("\nTesting motion profiling...")
    
    try:
        from motion_profiling.simple_api import create_path_from_beziers, get_duration
        from utils.bezier import Bezier
        from utils.point import Point
        
        # Create a simple path
        bezier = Bezier(Point(0, 0), Point(1, 0), Point(2, 1), Point(3, 1))
        beziers = [bezier]
        
        path = create_path_from_beziers(beziers, 0.0, 0.0)
        duration = get_duration(path)
        
        assert duration > 0, "Motion profiling duration should be positive"
        print(f"✓ Motion profiling working correctly (duration: {duration}ms)")
        
        return True
        
    except Exception as e:
        print(f"✗ Motion profiling test failed: {e}")
        return False


def test_json_serialization():
    """Test complete JSON serialization of path data."""
    print("\nTesting JSON serialization...")
    
    try:
        from utils.bezier import Bezier
        from utils.point import Point
        from utils.command import Command
        
        # Create test data
        beziers = [
            Bezier(Point(0, 0), Point(1, 0), Point(2, 1), Point(3, 1)),
            Bezier(Point(3, 1), Point(4, 1), Point(5, 2), Point(6, 2))
        ]
        
        commands = [
            Command(0.5, "start_intake"),
            Command(1.5, "stop_intake")
        ]
        
        # Create complete data structure
        data = {
            "start_speed": 0.0,
            "end_speed": 0.0,
            "segments": [bezier.to_json() for bezier in beziers],
            "commands": [cmd.to_json() for cmd in commands]
        }
        
        # Test JSON serialization
        json_str = json.dumps(data, indent=2)
        restored_data = json.loads(json_str)
        
        # Verify restoration
        restored_beziers = [Bezier.from_json(seg) for seg in restored_data["segments"]]
        restored_commands = [Command.from_json(cmd) for cmd in restored_data["commands"]]
        
        assert len(restored_beziers) == 2, "Bezier restoration failed"
        assert len(restored_commands) == 2, "Command restoration failed"
        assert restored_commands[0].name == "start_intake", "Command name restoration failed"
        
        print("✓ JSON serialization working correctly")
        return True
        
    except Exception as e:
        print(f"✗ JSON serialization test failed: {e}")
        return False


def test_file_operations():
    """Test file save/load operations."""
    print("\nTesting file operations...")
    
    try:
        from utils.bezier import Bezier
        from utils.point import Point
        from utils.command import Command
        
        # Create test data
        data = {
            "start_speed": 10.0,
            "end_speed": 5.0,
            "segments": [
                Bezier(Point(0, 0), Point(1, 0), Point(2, 1), Point(3, 1)).to_json()
            ],
            "commands": [
                Command(1.0, "test_command").to_json()
            ]
        }
        
        # Test file save/load
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(data, f, indent=2)
            temp_file = f.name
        
        try:
            # Load and verify
            with open(temp_file, 'r') as f:
                loaded_data = json.load(f)
            
            assert loaded_data["start_speed"] == 10.0, "Start speed not preserved"
            assert len(loaded_data["segments"]) == 1, "Segments not preserved"
            assert len(loaded_data["commands"]) == 1, "Commands not preserved"
            
            print("✓ File operations working correctly")
            return True
            
        finally:
            os.unlink(temp_file)
        
    except Exception as e:
        print(f"✗ File operations test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("AL Planner - Python Implementation Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_core_classes,
        test_motion_profiling,
        test_json_serialization,
        test_file_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'=' * 50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The application should work correctly.")
        print("\nTo run the application:")
        print("  python main.py")
        return 0
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
