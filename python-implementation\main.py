#!/usr/bin/env python3
"""
AL Planner - Python Implementation
Main application entry point.
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.main_window import MainWindow
from config.constants import WINDOW_TITLE, WINDOW_WIDTH, WINDOW_HEIGHT


def main():
    """Main application entry point."""
    try:
        # Create the main tkinter root window
        root = tk.Tk()
        root.title(WINDOW_TITLE)
        root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        
        # Set minimum window size
        root.minsize(800, 600)
        
        # Create and run the main application
        app = MainWindow(root)
        
        # Start the GUI event loop
        root.mainloop()
        
    except Exception as e:
        # Show error dialog if something goes wrong
        messagebox.showerror("Error", f"Failed to start AL Planner:\n{str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
