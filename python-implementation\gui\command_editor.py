"""
Command editor for timed commands during path following.
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Callable, Optional

from utils.command import Command


class CommandEditor(ttk.Frame):
    """
    Editor for managing timed commands along the path.
    """
    
    def __init__(self, parent, on_change_callback: Optional[Callable] = None):
        """
        Initialize command editor.
        
        Args:
            parent: Parent widget
            on_change_callback: Callback when commands change
        """
        super().__init__(parent)
        self.on_change_callback = on_change_callback
        self.commands: List[Command] = []
        self.max_time = 1.0  # Maximum time for sliders
        
        self._setup_layout()
    
    def _setup_layout(self):
        """Set up the editor layout."""
        # Add command button
        add_frame = ttk.Frame(self)
        add_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.add_button = ttk.Button(
            add_frame,
            text="Add Command",
            command=self._add_command
        )
        self.add_button.pack()
        
        # Scrollable frame for commands
        self.canvas = tk.Canvas(self, height=200)
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
    
    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def set_commands(self, commands: List[Command]):
        """Set the list of commands."""
        self.commands = [cmd.copy() for cmd in commands]
        self._update_command_controls()
    
    def get_commands(self) -> List[Command]:
        """Get the current list of commands."""
        return [cmd.copy() for cmd in self.commands]
    
    def set_max_time(self, max_time: float):
        """Set the maximum time for command sliders."""
        self.max_time = max(max_time, 1.0)
        self._update_command_controls()
    
    def _add_command(self):
        """Add a new command."""
        new_command = Command(0.0, "new_command")
        self.commands.append(new_command)
        self._update_command_controls()
        self._notify_change()
    
    def _remove_command(self, index: int):
        """Remove a command by index."""
        if 0 <= index < len(self.commands):
            del self.commands[index]
            self._update_command_controls()
            self._notify_change()
    
    def _update_command_controls(self):
        """Update the command control widgets."""
        # Clear existing controls
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # Create controls for each command
        for i, command in enumerate(self.commands):
            self._create_command_control(i, command)
    
    def _create_command_control(self, index: int, command: Command):
        """Create controls for a single command."""
        frame = ttk.Frame(self.scrollable_frame)
        frame.pack(fill=tk.X, pady=2)
        
        # Remove button
        remove_button = ttk.Button(
            frame,
            text="×",
            width=3,
            command=lambda: self._remove_command(index)
        )
        remove_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # Time slider
        time_var = tk.DoubleVar(value=command.t)
        time_scale = ttk.Scale(
            frame,
            from_=0.0,
            to=self.max_time,
            variable=time_var,
            command=lambda v: self._on_command_time_change(index, float(v)),
            length=150
        )
        time_scale.pack(side=tk.LEFT, padx=5)
        
        # Time label
        time_label = ttk.Label(frame, text=f"{command.t:.3f}")
        time_label.pack(side=tk.LEFT, padx=5)
        
        # Name entry
        name_var = tk.StringVar(value=command.name)
        name_entry = ttk.Entry(
            frame,
            textvariable=name_var,
            width=20
        )
        name_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # Bind name change
        name_var.trace('w', lambda *args: self._on_command_name_change(index, name_var.get()))
        
        # Store references for updates
        frame.time_var = time_var
        frame.time_label = time_label
        frame.name_var = name_var
    
    def _on_command_time_change(self, index: int, time: float):
        """Handle command time change."""
        if 0 <= index < len(self.commands):
            self.commands[index].t = time
            
            # Update label
            frames = self.scrollable_frame.winfo_children()
            if index < len(frames):
                frames[index].time_label.config(text=f"{time:.3f}")
            
            # Sort commands by time
            self.commands.sort(key=lambda cmd: cmd.t)
            self._update_command_controls()
            self._notify_change()
    
    def _on_command_name_change(self, index: int, name: str):
        """Handle command name change."""
        if 0 <= index < len(self.commands):
            self.commands[index].name = name
            self._notify_change()
    
    def _notify_change(self):
        """Notify that commands have changed."""
        if self.on_change_callback:
            self.on_change_callback()
