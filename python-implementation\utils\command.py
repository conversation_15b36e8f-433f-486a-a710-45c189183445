"""
Command system for timed actions during path following.
"""

from typing import Dict, Any


class Command:
    """
    Represents a timed command that executes during path following.
    """
    
    def __init__(self, t: float, name: str):
        """
        Initialize command.
        
        Args:
            t: Time parameter (0.0 to number of bezier segments)
            name: Command name/description
        """
        self.t = t
        self.name = name
    
    @classmethod
    def from_json(cls, json_data: Dict[str, Any]) -> 'Command':
        """Create Command from JSON data."""
        return cls(json_data['t'], json_data['name'])
    
    def to_json(self) -> Dict[str, Any]:
        """Convert to JSON-serializable dictionary."""
        return {
            't': round(self.t, 3),
            'name': self.name
        }
    
    def copy(self) -> 'Command':
        """Create a copy of this command."""
        return Command(self.t, self.name)
    
    def __str__(self) -> str:
        """String representation."""
        return f"Command(t={self.t:.3f}, name='{self.name}')"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return self.__str__()
    
    def __lt__(self, other) -> bool:
        """Less than comparison for sorting by time."""
        if isinstance(other, Command):
            return self.t < other.t
        return NotImplemented
    
    def __eq__(self, other) -> bool:
        """Equality comparison."""
        if isinstance(other, Command):
            return abs(self.t - other.t) < 1e-6 and self.name == other.name
        return False
