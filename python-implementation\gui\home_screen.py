"""
Home screen with file browser and path editing interface.
"""

import os
import tkinter as tk
from tkinter import ttk
from typing import TYPE_CHECKING

from .pathing_screen import PathingScreen

if TYPE_CHECKING:
    from .main_window import MainWindow


class HomeScreen(ttk.Frame):
    """
    Home screen that shows file browser and pathing interface.
    """
    
    def __init__(self, parent, main_window: 'MainWindow'):
        """
        Initialize the home screen.
        
        Args:
            parent: Parent widget
            main_window: Reference to main window
        """
        super().__init__(parent)
        self.main_window = main_window
        
        # Create the main layout
        self._setup_layout()
    
    def _setup_layout(self):
        """Set up the main layout."""
        # Create horizontal paned window
        self.paned_window = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - file browser
        self.file_frame = ttk.Frame(self.paned_window)
        self.paned_window.add(self.file_frame, weight=1)
        
        # Right panel - pathing screen
        self.pathing_frame = ttk.Frame(self.paned_window)
        self.paned_window.add(self.pathing_frame, weight=4)
        
        # Set up file browser
        self._setup_file_browser()
        
        # Set up pathing screen
        self.pathing_screen = PathingScreen(self.pathing_frame, self.main_window)
        self.pathing_screen.pack(fill=tk.BOTH, expand=True)
    
    def _setup_file_browser(self):
        """Set up the file browser panel."""
        # Title
        title_label = ttk.Label(self.file_frame, text="Project Files", font=("Arial", 12, "bold"))
        title_label.pack(pady=(10, 5))
        
        # File list
        self.file_listbox = tk.Listbox(self.file_frame, selectmode=tk.SINGLE)
        self.file_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Bind selection event
        self.file_listbox.bind('<<ListboxSelect>>', self._on_file_select)
        
        # Status label
        self.status_label = ttk.Label(self.file_frame, text="No project open", foreground="gray")
        self.status_label.pack(pady=(0, 10))
        
        # Buttons frame
        button_frame = ttk.Frame(self.file_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Open project button
        self.open_button = ttk.Button(
            button_frame, 
            text="Open Project", 
            command=self.main_window.open_project
        )
        self.open_button.pack(fill=tk.X, pady=(0, 5))
        
        # Refresh button
        self.refresh_button = ttk.Button(
            button_frame,
            text="Refresh",
            command=self._refresh_file_list
        )
        self.refresh_button.pack(fill=tk.X)
    
    def _on_file_select(self, event):
        """Handle file selection in the listbox."""
        selection = self.file_listbox.curselection()
        if selection:
            index = selection[0]
            self.main_window.set_current_file_index(index)
    
    def _refresh_file_list(self):
        """Refresh the file list."""
        if self.main_window.current_directory:
            self.main_window.open_directory(self.main_window.current_directory)
    
    def on_file_changed(self):
        """Called when the current file changes."""
        # Update file list
        self.file_listbox.delete(0, tk.END)
        
        file_list = self.main_window.get_file_list()
        current_index = self.main_window.get_current_file_index()
        
        if file_list:
            for file_path in file_list:
                filename = os.path.basename(file_path)
                self.file_listbox.insert(tk.END, filename)
            
            # Select current file
            if 0 <= current_index < len(file_list):
                self.file_listbox.selection_set(current_index)
                self.file_listbox.see(current_index)
            
            # Update status
            self.status_label.config(
                text=f"{len(file_list)} files in project",
                foreground="black"
            )
        else:
            if self.main_window.current_directory:
                self.status_label.config(
                    text="No JSON files found",
                    foreground="orange"
                )
            else:
                self.status_label.config(
                    text="No project open",
                    foreground="gray"
                )
        
        # Notify pathing screen
        self.pathing_screen.on_file_changed()
    
    def on_file_saved(self):
        """Called when the current file is saved."""
        # Notify pathing screen
        self.pathing_screen.on_file_saved()
