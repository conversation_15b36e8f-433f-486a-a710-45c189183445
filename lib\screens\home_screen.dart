import 'package:al_planner/screens/pathing_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  final List<File> _fileOptions = <File>[];
  Directory directory = Directory("/");
  String currentJson = "";

  Color buttonColor = const Color(0xffff9700);

  @override
  void initState() {
    super.initState();
    _loadDirectory();
  }

  Future<void> _loadDirectory() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _openDirectory(prefs.getString('directory') ?? "/");
    });
  }

  Future<void> _saveDirectory() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setString('directory', directory.absolute.path);
  }

  void _openDirectory(String? selectedDirectory) {
    if (selectedDirectory == null) {
      return;
    }
    setState(() {
      _fileOptions.clear();
    });

    directory = Directory(selectedDirectory);

    _saveDirectory();

    directory.list().listen((event) {
      if (extension(event.path) == '.json') {
        setState(() {
          _fileOptions.add(File(event.path));
        });
      }
    }).onDone(() {
      setState(() {
        _fileOptions
            .sort((a, b) => absolute(a.path).compareTo(absolute(b.path)));
      });
    });
  }

  void save() {
    if (_fileOptions.isNotEmpty) {
      File file = _fileOptions[_selectedIndex];
      file.writeAsStringSync(currentJson, flush: true);
    }

    setState(() {
      buttonColor = const Color(0xFFFF9700);
    });
  }

  void saveAs() {
    FilePicker.platform
        .saveFile(
      dialogTitle: 'Please select an output file:',
      fileName: 'output-file.json',
    )
        .then((outputFile) {
      File file = File(outputFile!);
      file.writeAsStringSync(currentJson);
      _openDirectory(directory.path);
    });
  }

  void open() {
    FilePicker.platform.getDirectoryPath().then((selectedDirectory) {
      _openDirectory(selectedDirectory);
    });
  }

  @override
  Widget build(BuildContext context) {
    return CallbackShortcuts(
      bindings: <ShortcutActivator, VoidCallback> {
        const SingleActivator(LogicalKeyboardKey.keyS, control: true, shift: true): () {
          saveAs();
        },
        const SingleActivator(LogicalKeyboardKey.keyO, control: true): () {
          open();
        },
        const SingleActivator(LogicalKeyboardKey.keyS, control: true): () {
          save();
        }
      },
      child: Focus(
        child: Scaffold(
          appBar: AppBar(
              backgroundColor: Theme.of(context).colorScheme.secondary,
              title: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: SelectableText(
                        _fileOptions.isEmpty
                            ? "Open path!"
                            : basename(_fileOptions[_selectedIndex].path),
                        onTap: () async {
                      await Clipboard.setData(ClipboardData(
                          text: _fileOptions.isEmpty
                              ? "Open path!"
                              : basename(_fileOptions[_selectedIndex].path)));
                      // copied successfully
                    }),
                  ),
                ],
              )),
          body: PlatformMenuBar(
            menus: <PlatformMenuItem>[
              PlatformMenu(label: "AL Planner", menus: <PlatformMenuItem>[
                if (PlatformProvidedMenuItem.hasMenu(
                    PlatformProvidedMenuItemType.about))
                  const PlatformProvidedMenuItem(
                      type: PlatformProvidedMenuItemType.about),
                if (PlatformProvidedMenuItem.hasMenu(
                    PlatformProvidedMenuItemType.quit))
                  const PlatformProvidedMenuItem(
                      type: PlatformProvidedMenuItemType.quit),
              ]),
              PlatformMenu(label: "File", menus: [
                PlatformMenuItem(
                    label: "Open Project",
                    onSelected: () {
                      open();
                    },
                    shortcut:
                        const SingleActivator(LogicalKeyboardKey.keyO, meta: true)),
                PlatformMenuItem(
                    label: "Save file",
                    onSelected: () {
                      save();
                    },
                    shortcut:
                        const SingleActivator(LogicalKeyboardKey.keyS, meta: true)),
                PlatformMenuItem(
                    label: "Save as",
                    onSelected: () {
                      saveAs();
                    },
                    shortcut: const SingleActivator(LogicalKeyboardKey.keyS,
                        meta: true, shift: true)),
                PlatformMenuItem(
                    label: "Next file",
                    onSelected: () {
                      setState(() {
                        _selectedIndex++;
                        _selectedIndex %= _fileOptions.length;
                      });
                    },
                    shortcut:
                        const SingleActivator(LogicalKeyboardKey.keyK, meta: true)),
                PlatformMenuItem(
                    label: "Last file",
                    onSelected: () {
                      setState(() {
                        _selectedIndex--;
                        _selectedIndex %= _fileOptions.length;
                      });
                    },
                    shortcut:
                        const SingleActivator(LogicalKeyboardKey.keyI, meta: true)),
                PlatformMenuItem(
                    label: "Open Project",
                    onSelected: () {
                      open();
                    },
                    shortcut:
                        const SingleActivator(LogicalKeyboardKey.keyO, meta: true)),
              ]),
            ],
            child: _fileOptions.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.folder_open,
                          size: 120,
                          color: Color(0xffff9700),
                        ),
                        const SizedBox(height: 24),
                        const Text(
                          "Welcome to AL Planner",
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: Color(0xffff9700),
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          "No project files found in the current directory",
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 32),
                        ElevatedButton.icon(
                          onPressed: open,
                          icon: const Icon(Icons.folder_open),
                          label: const Text("Open Project Directory"),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xffff9700),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 32,
                              vertical: 16,
                            ),
                            textStyle: const TextStyle(fontSize: 16),
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          "Or use the menu (☰) to access navigation options",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 24),
                        Container(
                          padding: const EdgeInsets.all(16),
                          margin: const EdgeInsets.symmetric(horizontal: 32),
                          decoration: BoxDecoration(
                            color: Colors.grey.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Column(
                            children: [
                              Text(
                                "💡 Quick Start",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                "1. Click 'Open Project Directory' to select a folder\n"
                                "2. Choose a directory containing JSON path files\n"
                                "3. Select files from the navigation menu to start editing paths",
                                style: TextStyle(fontSize: 14),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
                : PathingScreen(_fileOptions[_selectedIndex], (String value) {
                    currentJson = value;
                  }, true),
          ),
          drawer: Drawer(
            backgroundColor: const Color(0xfff5e6cf),
            child: Column(
              children: [
                // Drawer Header
                DrawerHeader(
                  decoration: const BoxDecoration(
                    color: Color(0xffff9700),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'AL Planner',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Current Directory:',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        directory.path,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 11,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                // Navigation Options
                ListTile(
                  leading: const Icon(Icons.folder_open),
                  title: const Text('Open Project Directory'),
                  onTap: () {
                    Navigator.pop(context);
                    open();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.save),
                  title: const Text('Save Current File'),
                  enabled: _fileOptions.isNotEmpty,
                  onTap: _fileOptions.isEmpty ? null : () {
                    Navigator.pop(context);
                    save();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.save_as),
                  title: const Text('Save As...'),
                  enabled: _fileOptions.isNotEmpty,
                  onTap: _fileOptions.isEmpty ? null : () {
                    Navigator.pop(context);
                    saveAs();
                  },
                ),
                const Divider(),
                // File List Header
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    children: [
                      const Icon(Icons.description, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'Project Files (${_fileOptions.length})',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                // File List
                Expanded(
                  child: _fileOptions.isEmpty
                      ? const Center(
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: Text(
                              'No JSON files found in current directory.\n\nUse "Open Project Directory" to select a folder containing JSON path files.',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        )
                      : ListView.builder(
                          padding: EdgeInsets.zero,
                          itemBuilder: (BuildContext context, int index) {
                            return ListTile(
                              title: Text(
                                basename(_fileOptions[index].path),
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              selected: _selectedIndex == index,
                              onTap: () {
                                setState(() {
                                  _selectedIndex = index;
                                });
                                Navigator.pop(context);
                              },
                            );
                          },
                          itemCount: _fileOptions.length,
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

showAlertDialog(BuildContext context, String stdout) {
  AlertDialog alert = AlertDialog(
    title: const Text("Error"),
    content: Text(stdout),
  );

  // show the dialog
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return alert;
    },
  );
}
