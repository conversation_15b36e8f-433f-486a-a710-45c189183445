"""
Chart display for velocity and angular velocity visualization.
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Optional

# Try to import matplotlib, fall back gracefully if not available
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False


class ChartDisplay(ttk.Frame):
    """
    Display for velocity and angular velocity charts.
    """
    
    def __init__(self, parent):
        """
        Initialize chart display.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        if MATPLOTLIB_AVAILABLE:
            self._setup_matplotlib_charts()
        else:
            self._setup_fallback_display()
    
    def _setup_matplotlib_charts(self):
        """Set up matplotlib charts."""
        # Create figure with subplots
        self.figure = Figure(figsize=(10, 6), dpi=100)
        
        # Velocity chart
        self.velocity_ax = self.figure.add_subplot(2, 1, 1)
        self.velocity_ax.set_title("Linear Velocity")
        self.velocity_ax.set_xlabel("Time (s)")
        self.velocity_ax.set_ylabel("Velocity (in/s)")
        self.velocity_ax.grid(True, alpha=0.3)
        
        # Angular velocity chart
        self.angular_ax = self.figure.add_subplot(2, 1, 2)
        self.angular_ax.set_title("Angular Velocity")
        self.angular_ax.set_xlabel("Time (s)")
        self.angular_ax.set_ylabel("Angular Velocity (deg/s)")
        self.angular_ax.grid(True, alpha=0.3)
        
        # Adjust layout
        self.figure.tight_layout()
        
        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.figure, self)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Initialize empty plots
        self.velocity_line, = self.velocity_ax.plot([], [], 'b-', linewidth=2)
        self.angular_line, = self.angular_ax.plot([], [], 'r-', linewidth=2)
    
    def _setup_fallback_display(self):
        """Set up fallback display when matplotlib is not available."""
        label = ttk.Label(
            self,
            text="Matplotlib not available.\nVelocity charts disabled.\n\nInstall matplotlib to enable charts:\npip install matplotlib",
            justify=tk.CENTER,
            foreground="gray"
        )
        label.pack(expand=True)
    
    def update_charts(self, times: List[float], velocities: List[float], angular_velocities: List[float]):
        """
        Update the velocity charts.
        
        Args:
            times: List of time values
            velocities: List of linear velocity values
            angular_velocities: List of angular velocity values
        """
        if not MATPLOTLIB_AVAILABLE:
            return
        
        if not times or not velocities or not angular_velocities:
            self.clear_charts()
            return
        
        # Update velocity chart
        self.velocity_line.set_data(times, velocities)
        self.velocity_ax.relim()
        self.velocity_ax.autoscale_view()
        
        # Update angular velocity chart
        self.angular_line.set_data(times, angular_velocities)
        self.angular_ax.relim()
        self.angular_ax.autoscale_view()
        
        # Redraw
        self.canvas.draw()
    
    def clear_charts(self):
        """Clear the charts."""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        self.velocity_line.set_data([], [])
        self.angular_line.set_data([], [])
        
        # Reset axes
        self.velocity_ax.relim()
        self.velocity_ax.autoscale_view()
        self.angular_ax.relim()
        self.angular_ax.autoscale_view()
        
        self.canvas.draw()


class SimpleChartDisplay(ttk.Frame):
    """
    Simple text-based chart display as fallback.
    """
    
    def __init__(self, parent):
        """Initialize simple chart display."""
        super().__init__(parent)
        
        # Create text widgets for displaying chart data
        velocity_frame = ttk.LabelFrame(self, text="Linear Velocity", padding=5)
        velocity_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        self.velocity_text = tk.Text(velocity_frame, height=8, wrap=tk.WORD)
        velocity_scrollbar = ttk.Scrollbar(velocity_frame, orient="vertical", command=self.velocity_text.yview)
        self.velocity_text.configure(yscrollcommand=velocity_scrollbar.set)
        
        self.velocity_text.pack(side="left", fill="both", expand=True)
        velocity_scrollbar.pack(side="right", fill="y")
        
        angular_frame = ttk.LabelFrame(self, text="Angular Velocity", padding=5)
        angular_frame.pack(fill=tk.BOTH, expand=True)
        
        self.angular_text = tk.Text(angular_frame, height=8, wrap=tk.WORD)
        angular_scrollbar = ttk.Scrollbar(angular_frame, orient="vertical", command=self.angular_text.yview)
        self.angular_text.configure(yscrollcommand=angular_scrollbar.set)
        
        self.angular_text.pack(side="left", fill="both", expand=True)
        angular_scrollbar.pack(side="right", fill="y")
    
    def update_charts(self, times: List[float], velocities: List[float], angular_velocities: List[float]):
        """Update the text-based charts."""
        # Clear existing content
        self.velocity_text.delete(1.0, tk.END)
        self.angular_text.delete(1.0, tk.END)
        
        if not times:
            return
        
        # Display velocity data
        self.velocity_text.insert(tk.END, "Time (s) | Velocity (in/s)\n")
        self.velocity_text.insert(tk.END, "-" * 25 + "\n")
        
        for i in range(0, len(times), max(1, len(times) // 20)):  # Show ~20 data points
            t = times[i]
            v = velocities[i] if i < len(velocities) else 0
            self.velocity_text.insert(tk.END, f"{t:6.2f}   | {v:8.2f}\n")
        
        # Display angular velocity data
        self.angular_text.insert(tk.END, "Time (s) | Angular Vel (deg/s)\n")
        self.angular_text.insert(tk.END, "-" * 30 + "\n")
        
        for i in range(0, len(times), max(1, len(times) // 20)):  # Show ~20 data points
            t = times[i]
            av = angular_velocities[i] if i < len(angular_velocities) else 0
            self.angular_text.insert(tk.END, f"{t:6.2f}   | {av:10.2f}\n")
    
    def clear_charts(self):
        """Clear the text displays."""
        self.velocity_text.delete(1.0, tk.END)
        self.angular_text.delete(1.0, tk.END)
