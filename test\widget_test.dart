// This is a basic Flutter widget test for AL Planner navigation.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:al_planner/main.dart';

void main() {
  testWidgets('AL Planner navigation test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the welcome screen is displayed when no files are loaded
    expect(find.text('Welcome to AL Planner'), findsOneWidget);
    expect(find.text('Open Project Directory'), findsOneWidget);

    // Verify that the drawer can be opened
    expect(find.byIcon(Icons.menu), findsOneWidget);

    // Open the drawer
    await tester.tap(find.byIcon(Icons.menu));
    await tester.pumpAndSettle();

    // Verify drawer content
    expect(find.text('AL Planner'), findsOneWidget);
    expect(find.text('Open Project Directory'), findsAtLeastNWidgets(1));
    expect(find.text('Save Current File'), findsOneWidget);
    expect(find.text('Save As...'), findsOneWidget);

    // Verify that the file list section is present
    expect(find.textContaining('Project Files'), findsOneWidget);
    expect(find.text('No JSON files found in current directory.'), findsOneWidget);
  });

  testWidgets('Welcome screen buttons test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the main action button is present and functional
    expect(find.widgetWithText(ElevatedButton, 'Open Project Directory'), findsOneWidget);

    // Verify help text is present
    expect(find.text('Or use the menu (☰) to access navigation options'), findsOneWidget);
    expect(find.text('💡 Quick Start'), findsOneWidget);
  });
}
