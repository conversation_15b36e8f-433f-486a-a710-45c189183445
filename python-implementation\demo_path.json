{"start_speed": 0.0, "end_speed": 0.0, "segments": [{"inverted": false, "stop_end": false, "is_straight_line": true, "path": [{"x": 1.207, "y": -1.635}, {"x": 1.207, "y": -1.489}, {"x": 1.206, "y": -1.344}, {"x": 1.206, "y": -1.199}], "constraints": {"velocity": 1.575, "accel": 4.572}}, {"inverted": false, "stop_end": false, "is_straight_line": true, "path": [{"x": 1.206, "y": -1.199}, {"x": 1.199, "y": -1.058}, {"x": 1.199, "y": -0.918}, {"x": 1.199, "y": -0.778}], "constraints": {"velocity": 1.575, "accel": 4.572}}, {"inverted": false, "stop_end": false, "is_straight_line": true, "path": [{"x": 1.199, "y": -0.778}, {"x": 1.01, "y": -0.722}, {"x": 0.821, "y": -0.667}, {"x": 0.632, "y": -0.612}], "constraints": {"velocity": 1.575, "accel": 4.572}}, {"inverted": false, "stop_end": false, "is_straight_line": true, "path": [{"x": 0.632, "y": -0.612}, {"x": 0.469, "y": -0.458}, {"x": 0.306, "y": -0.305}, {"x": 0.143, "y": -0.152}], "constraints": {"velocity": 1.575, "accel": 4.572}}], "commands": []}