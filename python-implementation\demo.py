#!/usr/bin/env python3
"""
Demo script to showcase AL Planner functionality without GUI.
"""

import sys
import os
import json

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.point import Point
from utils.bezier import Bezier
from utils.command import Command
from motion_profiling.simple_api import create_path_from_beziers, get_duration, get_pose, get_velocity


def create_demo_path():
    """Create a demonstration path with multiple segments."""
    print("Creating demonstration path...")
    
    # Create a path that forms an 'S' curve
    beziers = []
    
    # Segment 1: Start moving forward
    bezier1 = Bezier(
        Point(0.5, 0.5),   # Start point
        Point(0.8, 0.5),   # Control point 1
        Point(1.2, 0.8),   # Control point 2
        Point(1.5, 1.0),   # End point
        path_max_speed=50.0,  # 50 in/s
        path_max_accel=100.0  # 100 in/s²
    )
    beziers.append(bezier1)
    
    # Segment 2: Curve to the right
    bezier2 = Bezier(
        Point(1.5, 1.0),
        Point(1.8, 1.2),
        Point(2.2, 1.5),
        Point(2.5, 1.8),
        path_max_speed=40.0,
        path_max_accel=80.0
    )
    beziers.append(bezier2)
    
    # Segment 3: Final approach (slower)
    bezier3 = Bezier(
        Point(2.5, 1.8),
        Point(2.8, 2.0),
        Point(3.0, 2.5),
        Point(3.2, 3.0),
        path_max_speed=25.0,
        path_max_accel=60.0,
        stop_end=True  # Stop at the end
    )
    beziers.append(bezier3)
    
    return beziers


def create_demo_commands():
    """Create demonstration commands."""
    commands = [
        Command(0.3, "start_intake"),
        Command(1.2, "deploy_arm"),
        Command(2.1, "activate_shooter"),
        Command(2.8, "stop_intake")
    ]
    return commands


def demonstrate_motion_profiling(beziers, start_speed=0.0, end_speed=0.0):
    """Demonstrate motion profiling calculations."""
    print(f"\nMotion Profiling Analysis:")
    print(f"Path segments: {len(beziers)}")
    print(f"Start speed: {start_speed:.1f} in/s")
    print(f"End speed: {end_speed:.1f} in/s")
    
    # Create path and calculate duration
    path = create_path_from_beziers(beziers, start_speed / 39.37, end_speed / 39.37)
    duration_ms = get_duration(path)
    duration_s = duration_ms / 1000.0
    
    print(f"Total duration: {duration_s:.2f} seconds")
    
    # Sample the path at various time points
    print(f"\nPath Analysis (sampled every 0.5 seconds):")
    print(f"{'Time (s)':<8} {'X (m)':<8} {'Y (m)':<8} {'Angle (°)':<10} {'Velocity (in/s)':<15}")
    print("-" * 60)
    
    sample_times = []
    t = 0.0
    while t <= duration_s:
        sample_times.append(t)
        t += 0.5
    
    if sample_times[-1] < duration_s:
        sample_times.append(duration_s)
    
    for t in sample_times:
        pose = get_pose(t)
        velocity = get_velocity(t)
        angle_deg = pose.theta * 180.0 / 3.14159
        
        print(f"{t:<8.1f} {pose.x:<8.2f} {pose.y:<8.2f} {angle_deg:<10.1f} {velocity:<15.1f}")
    
    return duration_s


def save_demo_project(beziers, commands, filename="demo_path.json"):
    """Save the demonstration project to a file."""
    data = {
        "start_speed": 0.0,
        "end_speed": 0.0,
        "segments": [bezier.to_json() for bezier in beziers],
        "commands": [cmd.to_json() for cmd in commands]
    }
    
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)
    
    print(f"\nDemo project saved to: {filename}")
    print(f"You can load this file in the AL Planner GUI application.")


def main():
    """Run the demonstration."""
    print("AL Planner - Python Implementation Demo")
    print("=" * 50)
    
    # Create demonstration path
    beziers = create_demo_path()
    commands = create_demo_commands()
    
    print(f"Created path with {len(beziers)} segments and {len(commands)} commands")
    
    # Show path details
    print(f"\nPath Segments:")
    for i, bezier in enumerate(beziers):
        print(f"  Segment {i+1}:")
        print(f"    Start: ({bezier.p1.x:.2f}, {bezier.p1.y:.2f})")
        print(f"    End: ({bezier.p4.x:.2f}, {bezier.p4.y:.2f})")
        print(f"    Max Speed: {bezier.path_max_speed:.1f} in/s")
        print(f"    Max Accel: {bezier.path_max_accel:.1f} in/s²")
        print(f"    Stop at end: {bezier.stop_end}")
    
    print(f"\nCommands:")
    for cmd in commands:
        print(f"  t={cmd.t:.1f}: {cmd.name}")
    
    # Demonstrate motion profiling
    duration = demonstrate_motion_profiling(beziers)
    
    # Calculate some statistics
    total_distance = sum(bezier.get_length_estimate() for bezier in beziers)
    avg_speed = (total_distance / duration) * 39.37  # Convert to in/s
    
    print(f"\nPath Statistics:")
    print(f"Total distance: {total_distance:.2f} meters")
    print(f"Average speed: {avg_speed:.1f} in/s")
    print(f"Peak speed: {max(bezier.path_max_speed for bezier in beziers):.1f} in/s")
    
    # Save the project
    save_demo_project(beziers, commands)
    
    print(f"\n" + "=" * 50)
    print(f"Demo completed successfully!")
    print(f"\nTo run the full GUI application:")
    print(f"  python main.py")
    print(f"\nTo load this demo path:")
    print(f"  1. Run the GUI application")
    print(f"  2. Open the current directory as a project")
    print(f"  3. Select 'demo_path.json' from the file list")


if __name__ == "__main__":
    main()
