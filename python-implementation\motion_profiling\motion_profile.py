"""
Motion profiling for path following with velocity and acceleration constraints.
"""

import math
from typing import List, Optional, Tuple
from utils.point import Point
from utils.robot import Pose
from .path_segment import PathSegment, Constraints
from config.constants import INCHES_TO_METERS


class MotionCommand:
    """
    A motion command with desired pose, velocity, and angular velocity.
    """
    
    def __init__(self, desired_pose: Pose, desired_velocity: float, desired_angular: float):
        """
        Initialize motion command.
        
        Args:
            desired_pose: Target pose (x, y, theta)
            desired_velocity: Target linear velocity in m/s
            desired_angular: Target angular velocity in rad/s
        """
        self.desired_pose = desired_pose
        self.desired_velocity = desired_velocity
        self.desired_angular = desired_angular
    
    def __str__(self) -> str:
        return f"MotionCommand(pose={self.desired_pose}, v={self.desired_velocity:.2f}, ω={self.desired_angular:.2f})"


class Path:
    """
    Complete path with segments and motion constraints.
    """
    
    def __init__(self, start_speed: float, end_speed: float, segments: List[PathSegment], commands: List = None):
        """
        Initialize path.
        
        Args:
            start_speed: Starting velocity in m/s
            end_speed: Ending velocity in m/s
            segments: List of path segments
            commands: List of timed commands (optional)
        """
        self.start_speed = start_speed
        self.end_speed = end_speed
        self.segments = segments
        self.commands = commands or []
    
    def get_total_length(self) -> float:
        """Get total path length estimate."""
        return sum(segment.get_length_estimate() for segment in self.segments)
    
    def evaluate_at_segment_param(self, segment_index: int, t: float) -> Point:
        """
        Evaluate path at a specific segment and parameter.
        
        Args:
            segment_index: Index of the segment
            t: Parameter within the segment (0 to 1)
            
        Returns:
            Point on the path
        """
        if 0 <= segment_index < len(self.segments):
            return self.segments[segment_index].evaluate(t)
        return Point(0, 0)
    
    def evaluate_at_global_param(self, t: float) -> Point:
        """
        Evaluate path at global parameter.
        
        Args:
            t: Global parameter (0 to len(segments))
            
        Returns:
            Point on the path
        """
        if not self.segments:
            return Point(0, 0)
        
        # Clamp t to valid range
        t = max(0.0, min(t, len(self.segments)))
        
        # Find which segment and local parameter
        segment_index = int(t)
        local_t = t - segment_index
        
        # Handle edge case where t equals number of segments
        if segment_index >= len(self.segments):
            segment_index = len(self.segments) - 1
            local_t = 1.0
        
        return self.evaluate_at_segment_param(segment_index, local_t)


class MotionProfile:
    """
    Motion profile generator for path following.
    """
    
    def __init__(self, path: Path, robot_width: float = 12.0 * INCHES_TO_METERS):
        """
        Initialize motion profile.
        
        Args:
            path: Path to follow
            robot_width: Robot width in meters
        """
        self.path = path
        self.robot_width = robot_width
        self.duration_ms = 0
        self.profile_points = []
        self._generate_profile()
    
    def _generate_profile(self):
        """Generate the motion profile."""
        if not self.path.segments:
            self.duration_ms = 0
            return
        
        # Simple motion profile generation
        # This is a simplified version - a full implementation would use
        # more sophisticated algorithms for optimal velocity profiling
        
        total_time = 0.0
        dt = 0.01  # 10ms time step
        
        profile_points = []
        
        for segment_idx, segment in enumerate(self.path.segments):
            segment_length = segment.get_length_estimate()
            max_velocity = segment.constraints.velocity
            max_accel = segment.constraints.accel
            
            # Simple trapezoidal velocity profile for this segment
            # Accelerate to max velocity, cruise, then decelerate
            
            # Time to reach max velocity
            accel_time = max_velocity / max_accel
            accel_distance = 0.5 * max_accel * accel_time * accel_time
            
            # Check if we can reach max velocity
            if 2 * accel_distance > segment_length:
                # Triangular profile - can't reach max velocity
                accel_distance = segment_length / 2
                accel_time = math.sqrt(2 * accel_distance / max_accel)
                cruise_time = 0
                max_velocity_actual = max_accel * accel_time
            else:
                # Trapezoidal profile
                cruise_distance = segment_length - 2 * accel_distance
                cruise_time = cruise_distance / max_velocity
                max_velocity_actual = max_velocity
            
            decel_time = accel_time
            total_segment_time = accel_time + cruise_time + decel_time
            
            # Generate points for this segment
            segment_start_time = total_time
            
            for i in range(int(total_segment_time / dt) + 1):
                t_segment = i * dt
                
                if t_segment > total_segment_time:
                    break
                
                # Calculate velocity based on phase
                if t_segment <= accel_time:
                    # Acceleration phase
                    velocity = max_accel * t_segment
                    distance_in_segment = 0.5 * max_accel * t_segment * t_segment
                elif t_segment <= accel_time + cruise_time:
                    # Cruise phase
                    velocity = max_velocity_actual
                    distance_in_segment = accel_distance + max_velocity_actual * (t_segment - accel_time)
                else:
                    # Deceleration phase
                    decel_t = t_segment - accel_time - cruise_time
                    velocity = max_velocity_actual - max_accel * decel_t
                    distance_in_segment = (accel_distance + max_velocity_actual * cruise_time + 
                                         max_velocity_actual * decel_t - 0.5 * max_accel * decel_t * decel_t)
                
                # Convert distance to parameter t within segment
                if segment_length > 0:
                    t_param = min(1.0, distance_in_segment / segment_length)
                else:
                    t_param = 0.0
                
                # Get position and orientation
                position = segment.evaluate(t_param)
                
                # Calculate heading from derivative
                if t_param < 1.0:
                    derivative = segment.get_derivative(t_param)
                    heading = math.atan2(derivative.y, derivative.x)
                else:
                    # Use previous heading for end point
                    heading = profile_points[-1].desired_pose.theta if profile_points else 0.0
                
                # Adjust for inverted segments
                if segment.inverted:
                    velocity = -velocity
                    heading += math.pi
                
                # Create motion command
                pose = Pose(position.x, position.y, heading)
                angular_velocity = 0.0  # Simplified - would calculate from curvature
                
                command = MotionCommand(pose, velocity, angular_velocity)
                profile_points.append((total_time + t_segment, command))
            
            total_time += total_segment_time
        
        self.duration_ms = int(total_time * 1000)
        self.profile_points = profile_points
    
    def get_duration_ms(self) -> int:
        """Get total duration in milliseconds."""
        return self.duration_ms
    
    def get_command_at_time(self, time_ms: int) -> Optional[MotionCommand]:
        """
        Get motion command at specific time.
        
        Args:
            time_ms: Time in milliseconds
            
        Returns:
            MotionCommand at that time, or None if out of range
        """
        time_s = time_ms / 1000.0
        
        if not self.profile_points or time_s < 0:
            return None
        
        if time_s >= self.profile_points[-1][0]:
            return self.profile_points[-1][1]
        
        # Find the appropriate command by interpolation
        for i in range(len(self.profile_points) - 1):
            t1, cmd1 = self.profile_points[i]
            t2, cmd2 = self.profile_points[i + 1]
            
            if t1 <= time_s <= t2:
                # Linear interpolation
                alpha = (time_s - t1) / (t2 - t1) if t2 > t1 else 0.0
                
                # Interpolate pose
                x = cmd1.desired_pose.x * (1 - alpha) + cmd2.desired_pose.x * alpha
                y = cmd1.desired_pose.y * (1 - alpha) + cmd2.desired_pose.y * alpha
                theta = cmd1.desired_pose.theta * (1 - alpha) + cmd2.desired_pose.theta * alpha
                
                # Interpolate velocities
                velocity = cmd1.desired_velocity * (1 - alpha) + cmd2.desired_velocity * alpha
                angular = cmd1.desired_angular * (1 - alpha) + cmd2.desired_angular * alpha
                
                return MotionCommand(Pose(x, y, theta), velocity, angular)
        
        return self.profile_points[0][1] if self.profile_points else None
