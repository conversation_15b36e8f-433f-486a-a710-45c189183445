"""
Robot simulation and live data streaming functionality.
"""

import json
import threading
import time
from typing import List, Optional, Callable
import urllib.request
import urllib.error

from utils.robot import RobotPosition


class RobotSimulation:
    """
    Handles robot simulation and live data streaming.
    """
    
    def __init__(self, on_robot_update: Optional[Callable[[List[RobotPosition]], None]] = None):
        """
        Initialize robot simulation.
        
        Args:
            on_robot_update: Callback when robot positions are updated
        """
        self.on_robot_update = on_robot_update
        self.is_streaming = False
        self.stream_thread = None
        self.stop_streaming = False
        self.stream_url = "http://***********/uart0"
        self.connection_timeout = 5.0
        self.retry_delay = 2.0
    
    def start_live_streaming(self, url: Optional[str] = None):
        """
        Start live data streaming from robot.
        
        Args:
            url: Optional URL to stream from (defaults to robot IP)
        """
        if self.is_streaming:
            return
        
        if url:
            self.stream_url = url
        
        self.is_streaming = True
        self.stop_streaming = False
        
        # Start streaming thread
        self.stream_thread = threading.Thread(target=self._stream_loop)
        self.stream_thread.daemon = True
        self.stream_thread.start()
    
    def stop_live_streaming(self):
        """Stop live data streaming."""
        self.is_streaming = False
        self.stop_streaming = True
        
        if self.stream_thread and self.stream_thread.is_alive():
            self.stream_thread.join(timeout=1.0)
    
    def _stream_loop(self):
        """Main streaming loop running in separate thread."""
        while not self.stop_streaming:
            try:
                self._attempt_connection()
            except Exception as e:
                print(f"Streaming error: {e}")
            
            if not self.stop_streaming:
                time.sleep(self.retry_delay)
    
    def _attempt_connection(self):
        """Attempt to connect and stream data."""
        try:
            # Create request with timeout
            request = urllib.request.Request(self.stream_url)
            request.add_header("Accept", "text/event-stream")
            
            with urllib.request.urlopen(request, timeout=self.connection_timeout) as response:
                print(f"Connected to robot at {self.stream_url}")
                
                # Read streaming data line by line
                for line in response:
                    if self.stop_streaming:
                        break
                    
                    try:
                        line_str = line.decode('utf-8').strip()
                        if line_str and not line_str.startswith('#'):
                            self._process_data_line(line_str)
                    except Exception as e:
                        print(f"Error processing line: {e}")
                        
        except urllib.error.URLError as e:
            print(f"Connection failed: {e}")
        except Exception as e:
            print(f"Streaming error: {e}")
    
    def _process_data_line(self, line: str):
        """
        Process a line of data from the robot.
        
        Args:
            line: JSON string containing robot data
        """
        try:
            # Parse JSON data
            data = json.loads(line)
            
            # Convert to robot positions
            robots = []
            if isinstance(data, list):
                for robot_data in data:
                    if len(robot_data) >= 3:
                        x, y, angle = robot_data[0], robot_data[1], robot_data[2]
                        robot = RobotPosition(x, y, angle)
                        robots.append(robot)
            
            # Notify callback
            if self.on_robot_update and robots:
                self.on_robot_update(robots)
                
        except json.JSONDecodeError:
            # Invalid JSON - ignore
            pass
        except Exception as e:
            print(f"Error processing robot data: {e}")
    
    def simulate_robot_at_time(self, t: float, total_time: float) -> List[RobotPosition]:
        """
        Simulate robot position at a specific time.
        
        Args:
            t: Current time
            total_time: Total path time
            
        Returns:
            List containing simulated robot position
        """
        if total_time <= 0:
            return []
        
        # Simple simulation - robot moves in a circle for demonstration
        import math
        
        # Normalize time to 0-1
        normalized_t = t / total_time
        
        # Create circular motion
        radius = 1.0
        center_x = 1.8
        center_y = 1.8
        
        angle = normalized_t * 2 * math.pi
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)
        heading = angle + math.pi / 2  # Tangent to circle
        
        robot = RobotPosition(x, y, heading)
        return [robot]
    
    def is_connected(self) -> bool:
        """Check if currently connected to live data stream."""
        return self.is_streaming and self.stream_thread and self.stream_thread.is_alive()


class MockRobotSimulation:
    """
    Mock robot simulation for testing without actual robot connection.
    """
    
    def __init__(self, on_robot_update: Optional[Callable[[List[RobotPosition]], None]] = None):
        """Initialize mock simulation."""
        self.on_robot_update = on_robot_update
        self.is_running = False
        self.simulation_thread = None
        self.stop_simulation = False
    
    def start_simulation(self):
        """Start mock robot simulation."""
        if self.is_running:
            return
        
        self.is_running = True
        self.stop_simulation = False
        
        self.simulation_thread = threading.Thread(target=self._simulation_loop)
        self.simulation_thread.daemon = True
        self.simulation_thread.start()
    
    def stop_simulation(self):
        """Stop mock simulation."""
        self.is_running = False
        self.stop_simulation = True
        
        if self.simulation_thread and self.simulation_thread.is_alive():
            self.simulation_thread.join(timeout=1.0)
    
    def _simulation_loop(self):
        """Mock simulation loop."""
        import math
        
        start_time = time.time()
        
        while not self.stop_simulation:
            current_time = time.time() - start_time
            
            # Create mock robot data
            robots = []
            
            # Robot 1 - moves in figure-8
            t1 = current_time * 0.5
            x1 = 1.8 + 0.8 * math.sin(t1)
            y1 = 1.8 + 0.4 * math.sin(2 * t1)
            angle1 = math.atan2(0.4 * 2 * math.cos(2 * t1), 0.8 * math.cos(t1))
            robots.append(RobotPosition(x1, y1, angle1))
            
            # Robot 2 - moves in circle (if multiple robots)
            t2 = current_time * 0.3
            x2 = 1.8 + 0.6 * math.cos(t2)
            y2 = 1.8 + 0.6 * math.sin(t2)
            angle2 = t2 + math.pi / 2
            robots.append(RobotPosition(x2, y2, angle2))
            
            # Notify callback
            if self.on_robot_update:
                self.on_robot_update(robots)
            
            time.sleep(1.0 / 30.0)  # 30 FPS
    
    def is_connected(self) -> bool:
        """Check if simulation is running."""
        return self.is_running
