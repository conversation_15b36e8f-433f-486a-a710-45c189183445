"""
Main application window with menu bar and file operations.
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from typing import Optional, List
import json

from .home_screen import HomeScreen
from config.constants import WINDOW_TITLE, SUPPORTED_EXTENSIONS


class MainWindow:
    """
    Main application window that manages the overall application state.
    """
    
    def __init__(self, root: tk.Tk):
        """
        Initialize the main window.
        
        Args:
            root: The tkinter root window
        """
        self.root = root
        self.current_directory: Optional[str] = None
        self.current_file: Optional[str] = None
        self.file_list: List[str] = []
        self.current_file_index: int = 0
        self.current_json: str = ""
        self.unsaved_changes: bool = False
        
        # Create the main interface
        self._setup_menu()
        self._setup_main_frame()
        self._setup_keyboard_shortcuts()
        
        # Load last directory from settings
        self._load_settings()
        
        # Set up window close handler
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _setup_menu(self):
        """Set up the menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        
        file_menu.add_command(label="Open Project", command=self.open_project, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="Save", command=self.save_file, accelerator="Ctrl+S")
        file_menu.add_command(label="Save As...", command=self.save_as, accelerator="Ctrl+Shift+S")
        file_menu.add_separator()
        file_menu.add_command(label="Next File", command=self.next_file, accelerator="Ctrl+K")
        file_menu.add_command(label="Previous File", command=self.previous_file, accelerator="Ctrl+I")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self._on_closing)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self._show_about)
    
    def _setup_main_frame(self):
        """Set up the main application frame."""
        # Create main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create home screen
        self.home_screen = HomeScreen(self.main_frame, self)
        self.home_screen.pack(fill=tk.BOTH, expand=True)
    
    def _setup_keyboard_shortcuts(self):
        """Set up keyboard shortcuts."""
        self.root.bind('<Control-o>', lambda e: self.open_project())
        self.root.bind('<Control-s>', lambda e: self.save_file())
        self.root.bind('<Control-S>', lambda e: self.save_as())  # Shift+S
        self.root.bind('<Control-k>', lambda e: self.next_file())
        self.root.bind('<Control-i>', lambda e: self.previous_file())
    
    def _load_settings(self):
        """Load application settings."""
        # Try to load last directory from a simple settings file
        settings_file = os.path.join(os.path.expanduser("~"), ".al_planner_settings.json")
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r') as f:
                    settings = json.load(f)
                    last_dir = settings.get('last_directory')
                    if last_dir and os.path.exists(last_dir):
                        self.open_directory(last_dir)
        except Exception:
            pass  # Ignore errors loading settings
    
    def _save_settings(self):
        """Save application settings."""
        if self.current_directory:
            settings_file = os.path.join(os.path.expanduser("~"), ".al_planner_settings.json")
            try:
                settings = {'last_directory': self.current_directory}
                with open(settings_file, 'w') as f:
                    json.dump(settings, f)
            except Exception:
                pass  # Ignore errors saving settings
    
    def open_project(self):
        """Open a project directory."""
        directory = filedialog.askdirectory(title="Select Project Directory")
        if directory:
            self.open_directory(directory)
    
    def open_directory(self, directory: str):
        """
        Open a directory and scan for JSON files.
        
        Args:
            directory: Path to the directory to open
        """
        if not os.path.exists(directory):
            messagebox.showerror("Error", f"Directory does not exist: {directory}")
            return
        
        self.current_directory = directory
        self.file_list = []
        
        # Scan for JSON files
        try:
            for filename in os.listdir(directory):
                if any(filename.lower().endswith(ext) for ext in SUPPORTED_EXTENSIONS):
                    self.file_list.append(os.path.join(directory, filename))
            
            # Sort files alphabetically
            self.file_list.sort()
            
            # Reset file index
            self.current_file_index = 0
            
            # Update the display
            self._update_current_file()
            self._save_settings()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to read directory: {str(e)}")
    
    def _update_current_file(self):
        """Update the current file and notify the home screen."""
        if self.file_list:
            self.current_file = self.file_list[self.current_file_index]
            filename = os.path.basename(self.current_file)
            self.root.title(f"{WINDOW_TITLE} - {filename}")
        else:
            self.current_file = None
            self.root.title(f"{WINDOW_TITLE} - No files")
        
        # Notify home screen of file change
        self.home_screen.on_file_changed()
    
    def next_file(self):
        """Switch to the next file in the list."""
        if len(self.file_list) > 1:
            self.current_file_index = (self.current_file_index + 1) % len(self.file_list)
            self._update_current_file()
    
    def previous_file(self):
        """Switch to the previous file in the list."""
        if len(self.file_list) > 1:
            self.current_file_index = (self.current_file_index - 1) % len(self.file_list)
            self._update_current_file()
    
    def save_file(self):
        """Save the current file."""
        if self.current_file and self.current_json:
            try:
                with open(self.current_file, 'w') as f:
                    f.write(self.current_json)
                self.unsaved_changes = False
                self.home_screen.on_file_saved()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save file: {str(e)}")
    
    def save_as(self):
        """Save the current data to a new file."""
        if not self.current_json:
            messagebox.showwarning("Warning", "No data to save")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Save As",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write(self.current_json)
                
                # If saved in current directory, refresh file list
                if self.current_directory and filename.startswith(self.current_directory):
                    self.open_directory(self.current_directory)
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save file: {str(e)}")
    
    def set_current_json(self, json_data: str):
        """
        Set the current JSON data.
        
        Args:
            json_data: JSON string to set
        """
        self.current_json = json_data
        self.unsaved_changes = True
    
    def get_current_file(self) -> Optional[str]:
        """Get the current file path."""
        return self.current_file
    
    def get_file_list(self) -> List[str]:
        """Get the list of files in the current directory."""
        return self.file_list.copy()
    
    def get_current_file_index(self) -> int:
        """Get the current file index."""
        return self.current_file_index
    
    def set_current_file_index(self, index: int):
        """Set the current file index."""
        if 0 <= index < len(self.file_list):
            self.current_file_index = index
            self._update_current_file()
    
    def _show_about(self):
        """Show about dialog."""
        about_text = """AL Planner - Python Implementation

An autonomous path planning application for robotics.

Features:
• Interactive Bezier curve path editor
• Real-time motion profiling
• Robot simulation and visualization
• Command timeline system
• JSON-based project files

Original Flutter/Dart version with Rust backend
converted to pure Python implementation."""
        
        messagebox.showinfo("About AL Planner", about_text)
    
    def _on_closing(self):
        """Handle window closing."""
        if self.unsaved_changes:
            result = messagebox.askyesnocancel(
                "Unsaved Changes",
                "You have unsaved changes. Do you want to save before closing?"
            )
            if result is True:  # Yes
                self.save_file()
            elif result is None:  # Cancel
                return
        
        self.root.destroy()
