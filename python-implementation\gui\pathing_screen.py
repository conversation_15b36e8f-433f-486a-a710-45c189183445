"""
Pathing screen with path editing controls and visualization.
"""

import os
import json
import tkinter as tk
from tkinter import ttk, messagebox
from typing import TYPE_CHECKING, List, Optional
import threading
import time

from .path_canvas import PathCanvas
from .velocity_controls import VelocityControls
from .command_editor import CommandEditor
from .chart_display import ChartDisplay
from .robot_simulation import RobotSimulation, MockRobotSimulation
from utils.bezier import Bezier
from utils.command import Command
from utils.robot import RobotPosition
from motion_profiling.simple_api import (
    create_path_from_beziers, get_duration, get_pose, 
    get_velocity, get_angular_velocity
)
from config.constants import MAX_SPEED, DEFAULT_START_SPEED, DEFAULT_END_SPEED

if TYPE_CHECKING:
    from .main_window import MainWindow


class PathingScreen(ttk.Frame):
    """
    Main pathing screen with canvas, controls, and charts.
    """
    
    def __init__(self, parent, main_window: 'MainWindow'):
        """
        Initialize the pathing screen.
        
        Args:
            parent: Parent widget
            main_window: Reference to main window
        """
        super().__init__(parent)
        self.main_window = main_window
        
        # Path data
        self.beziers: List[Bezier] = []
        self.commands: List[Command] = []
        self.robots: List[RobotPosition] = []
        self.start_speed = DEFAULT_START_SPEED
        self.end_speed = DEFAULT_END_SPEED
        self.is_skills_mode = False
        
        # Animation state
        self.is_playing = False
        self.current_time = 0.0
        self.total_time = 0.0
        self.animation_thread = None
        self.stop_animation = False

        # Robot simulation
        self.robot_simulation = None
        self.use_live_robot = False
        
        # Create the interface
        self._setup_layout()
        
        # Load current file if available
        self.on_file_changed()
    
    def _setup_layout(self):
        """Set up the main layout."""
        # Create main container with scrollbar
        canvas_frame = tk.Frame(self)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create scrollable frame
        self.canvas = tk.Canvas(canvas_frame)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel to canvas
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        
        # Create top controls
        self._create_top_controls()
        
        # Create main content area
        self._create_main_content()
        
        # Create command editor
        self._create_command_editor()
        
        # Create charts
        self._create_charts()
        
        # Create JSON editor
        self._create_json_editor()
    
    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def _create_top_controls(self):
        """Create the top control panel."""
        top_frame = ttk.Frame(self.scrollable_frame)
        top_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Skills/Match toggle and speed controls
        controls_frame1 = ttk.Frame(top_frame)
        controls_frame1.pack(fill=tk.X, pady=5)
        
        # Skills mode toggle
        self.skills_var = tk.BooleanVar()
        self.skills_button = ttk.Checkbutton(
            controls_frame1,
            text="Skills Mode",
            variable=self.skills_var,
            command=self._on_skills_toggle
        )
        self.skills_button.pack(side=tk.LEFT, padx=(0, 20))
        
        # Start speed control
        ttk.Label(controls_frame1, text="Start Speed:").pack(side=tk.LEFT)
        self.start_speed_var = tk.DoubleVar(value=self.start_speed)
        self.start_speed_scale = ttk.Scale(
            controls_frame1,
            from_=-MAX_SPEED,
            to=MAX_SPEED,
            variable=self.start_speed_var,
            command=self._on_start_speed_change,
            length=200
        )
        self.start_speed_scale.pack(side=tk.LEFT, padx=5)
        self.start_speed_label = ttk.Label(controls_frame1, text=f"{self.start_speed:.0f}")
        self.start_speed_label.pack(side=tk.LEFT, padx=5)
        
        # End speed control
        ttk.Label(controls_frame1, text="End Speed:").pack(side=tk.LEFT, padx=(20, 0))
        self.end_speed_var = tk.DoubleVar(value=self.end_speed)
        self.end_speed_scale = ttk.Scale(
            controls_frame1,
            from_=-MAX_SPEED,
            to=MAX_SPEED,
            variable=self.end_speed_var,
            command=self._on_end_speed_change,
            length=200
        )
        self.end_speed_scale.pack(side=tk.LEFT, padx=5)
        self.end_speed_label = ttk.Label(controls_frame1, text=f"{self.end_speed:.0f}")
        self.end_speed_label.pack(side=tk.LEFT, padx=5)
        
        # Animation controls
        controls_frame2 = ttk.Frame(top_frame)
        controls_frame2.pack(fill=tk.X, pady=5)
        
        # Play/Pause button
        self.play_button = ttk.Button(
            controls_frame2,
            text="Play",
            command=self._toggle_animation
        )
        self.play_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Flip button
        self.flip_button = ttk.Button(
            controls_frame2,
            text="Flip Path",
            command=self._flip_path
        )
        self.flip_button.pack(side=tk.LEFT, padx=(0, 10))

        # Live robot toggle
        self.live_robot_var = tk.BooleanVar()
        self.live_robot_button = ttk.Checkbutton(
            controls_frame2,
            text="Live Robot",
            variable=self.live_robot_var,
            command=self._toggle_live_robot
        )
        self.live_robot_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Time slider
        ttk.Label(controls_frame2, text="Time:").pack(side=tk.LEFT)
        self.time_var = tk.DoubleVar()
        self.time_scale = ttk.Scale(
            controls_frame2,
            from_=0.0,
            to=1.0,
            variable=self.time_var,
            command=self._on_time_change,
            length=300
        )
        self.time_scale.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.time_label = ttk.Label(controls_frame2, text="0.0s / 0.0s")
        self.time_label.pack(side=tk.LEFT, padx=5)
    
    def _create_main_content(self):
        """Create the main content area with canvas and velocity controls."""
        main_frame = ttk.Frame(self.scrollable_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create horizontal paned window
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # Left side - Path canvas
        canvas_frame = ttk.Frame(paned)
        paned.add(canvas_frame, weight=3)
        
        self.path_canvas = PathCanvas(canvas_frame, self._on_path_changed)
        self.path_canvas.pack(fill=tk.BOTH, expand=True)
        
        # Right side - Velocity controls
        controls_frame = ttk.Frame(paned)
        paned.add(controls_frame, weight=1)
        
        self.velocity_controls = VelocityControls(controls_frame, self._on_bezier_changed)
        self.velocity_controls.pack(fill=tk.BOTH, expand=True)
    
    def _create_command_editor(self):
        """Create the command editor section."""
        command_frame = ttk.LabelFrame(self.scrollable_frame, text="Commands", padding=10)
        command_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.command_editor = CommandEditor(command_frame, self._on_commands_changed)
        self.command_editor.pack(fill=tk.BOTH, expand=True)
    
    def _create_charts(self):
        """Create the velocity charts section."""
        chart_frame = ttk.LabelFrame(self.scrollable_frame, text="Velocity Charts", padding=10)
        chart_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.chart_display = ChartDisplay(chart_frame)
        self.chart_display.pack(fill=tk.BOTH, expand=True)
    
    def _create_json_editor(self):
        """Create the JSON editor section."""
        json_frame = ttk.LabelFrame(self.scrollable_frame, text="JSON Data", padding=10)
        json_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # JSON text editor
        self.json_text = tk.Text(json_frame, height=10, wrap=tk.WORD)
        json_scrollbar = ttk.Scrollbar(json_frame, orient="vertical", command=self.json_text.yview)
        self.json_text.configure(yscrollcommand=json_scrollbar.set)
        
        self.json_text.pack(side="left", fill="both", expand=True)
        json_scrollbar.pack(side="right", fill="y")
        
        # Bind text change event
        self.json_text.bind('<KeyRelease>', self._on_json_changed)
    
    def _on_skills_toggle(self):
        """Handle skills mode toggle."""
        self.is_skills_mode = self.skills_var.get()
        self.path_canvas.set_skills_mode(self.is_skills_mode)
    
    def _on_start_speed_change(self, value):
        """Handle start speed change."""
        self.start_speed = float(value)
        self.start_speed_label.config(text=f"{self.start_speed:.0f}")
        self._rebuild_motion_profile()
    
    def _on_end_speed_change(self, value):
        """Handle end speed change."""
        self.end_speed = float(value)
        self.end_speed_label.config(text=f"{self.end_speed:.0f}")
        self._rebuild_motion_profile()
    
    def _on_path_changed(self):
        """Handle path changes from canvas."""
        self.beziers = self.path_canvas.get_beziers()
        self.velocity_controls.set_beziers(self.beziers)
        self._rebuild_motion_profile()
        self._update_json()
    
    def _on_bezier_changed(self):
        """Handle bezier property changes from velocity controls."""
        self.beziers = self.velocity_controls.get_beziers()
        self.path_canvas.set_beziers(self.beziers)
        self._rebuild_motion_profile()
        self._update_json()
    
    def _on_commands_changed(self):
        """Handle command changes."""
        self.commands = self.command_editor.get_commands()
        command_dots = [cmd.t for cmd in self.commands]
        self.path_canvas.set_command_dots(command_dots)
        self._update_json()
    
    def _on_time_change(self, value):
        """Handle time slider change."""
        if not self.is_playing:
            self.current_time = float(value) * self.total_time
            self._update_robot_position()
    
    def _on_json_changed(self, event):
        """Handle JSON text changes."""
        # Debounce the JSON parsing to avoid constant updates
        if hasattr(self, '_json_timer'):
            self.after_cancel(self._json_timer)
        self._json_timer = self.after(500, self._parse_json)
    
    def _toggle_animation(self):
        """Toggle animation play/pause."""
        if self.is_playing:
            self._stop_animation()
        else:
            self._start_animation()
    
    def _flip_path(self):
        """Flip the entire path."""
        self.path_canvas.flip_all_beziers()
        self._on_path_changed()

    def _toggle_live_robot(self):
        """Toggle live robot data streaming."""
        self.use_live_robot = self.live_robot_var.get()

        if self.use_live_robot:
            self._start_robot_simulation()
        else:
            self._stop_robot_simulation()

    def _start_robot_simulation(self):
        """Start robot simulation or live streaming."""
        if self.robot_simulation:
            self._stop_robot_simulation()

        try:
            # Try live robot first
            self.robot_simulation = RobotSimulation(self._on_robot_data_received)
            self.robot_simulation.start_live_streaming()
            print("Started live robot streaming")
        except Exception:
            # Fall back to mock simulation
            self.robot_simulation = MockRobotSimulation(self._on_robot_data_received)
            self.robot_simulation.start_simulation()
            print("Started mock robot simulation")

    def _stop_robot_simulation(self):
        """Stop robot simulation."""
        if self.robot_simulation:
            if hasattr(self.robot_simulation, 'stop_live_streaming'):
                self.robot_simulation.stop_live_streaming()
            elif hasattr(self.robot_simulation, 'stop_simulation'):
                self.robot_simulation.stop_simulation()
            self.robot_simulation = None

    def _on_robot_data_received(self, robots: List[RobotPosition]):
        """Handle received robot data."""
        # Update robots in main thread
        self.after_idle(lambda: self._update_live_robots(robots))

    def _update_live_robots(self, robots: List[RobotPosition]):
        """Update live robot positions."""
        if self.use_live_robot:
            self.robots = robots
            self.path_canvas.set_robots(self.robots)
    
    def _rebuild_motion_profile(self):
        """Rebuild the motion profile and update charts."""
        if not self.beziers:
            self.total_time = 0.0
            self.time_scale.config(to=1.0)
            self.time_label.config(text="0.0s / 0.0s")
            self.chart_display.clear_charts()
            self.velocity_controls.set_total_time(0.0)
            self.command_editor.set_max_time(1.0)
            return

        try:
            # Create path and get duration
            path = create_path_from_beziers(self.beziers, self.start_speed / 39.37, self.end_speed / 39.37)
            duration_ms = get_duration(path)
            self.total_time = duration_ms / 1000.0

            # Update time controls
            self.time_scale.config(to=self.total_time)
            self.time_label.config(text=f"{self.current_time:.1f}s / {self.total_time:.1f}s")

            # Update velocity controls and command editor
            self.velocity_controls.set_total_time(self.total_time)
            self.command_editor.set_max_time(len(self.beziers))

            # Update charts
            self._update_charts()

        except Exception as e:
            print(f"Error rebuilding motion profile: {e}")
    
    def _update_charts(self):
        """Update velocity charts."""
        if self.total_time <= 0:
            return
        
        # Generate chart data
        times = []
        velocities = []
        angular_velocities = []
        
        num_points = min(200, int(self.total_time * 20))  # 20 points per second, max 200
        
        for i in range(num_points + 1):
            t = i * self.total_time / num_points
            times.append(t)
            velocities.append(get_velocity(t))
            angular_velocities.append(get_angular_velocity(t))
        
        self.chart_display.update_charts(times, velocities, angular_velocities)
    
    def _update_robot_position(self):
        """Update robot position based on current time."""
        # Don't update if using live robot data
        if self.use_live_robot:
            return

        if self.total_time <= 0:
            self.robots = []
        else:
            pose = get_pose(self.current_time)
            robot = RobotPosition(pose.x, pose.y, pose.theta)
            self.robots = [robot]

        self.path_canvas.set_robots(self.robots)
    
    def _start_animation(self):
        """Start the animation."""
        if self.total_time <= 0:
            return
        
        self.is_playing = True
        self.play_button.config(text="Pause")
        self.stop_animation = False
        
        # Reset if at end
        if self.current_time >= self.total_time:
            self.current_time = 0.0
        
        # Start animation thread
        self.animation_thread = threading.Thread(target=self._animation_loop)
        self.animation_thread.daemon = True
        self.animation_thread.start()
    
    def _stop_animation(self):
        """Stop the animation."""
        self.is_playing = False
        self.play_button.config(text="Play")
        self.stop_animation = True
    
    def _animation_loop(self):
        """Animation loop running in separate thread."""
        dt = 1.0 / 60.0  # 60 FPS
        
        while not self.stop_animation and self.current_time < self.total_time:
            self.current_time += dt
            
            # Update UI in main thread
            self.after_idle(self._update_animation_ui)
            
            time.sleep(dt)
        
        # Animation finished
        self.after_idle(self._stop_animation)
    
    def _update_animation_ui(self):
        """Update animation UI elements."""
        if self.total_time > 0:
            self.time_var.set(self.current_time)
            self.time_label.config(text=f"{self.current_time:.1f}s / {self.total_time:.1f}s")
            self._update_robot_position()
    
    def _update_json(self):
        """Update the JSON text display."""
        data = {
            "start_speed": self.start_speed,
            "end_speed": self.end_speed,
            "segments": [bezier.to_json() for bezier in self.beziers],
            "commands": [cmd.to_json() for cmd in self.commands]
        }
        
        json_str = json.dumps(data, indent=2)
        
        # Update text widget without triggering change event
        self.json_text.unbind('<KeyRelease>')
        self.json_text.delete(1.0, tk.END)
        self.json_text.insert(1.0, json_str)
        self.json_text.bind('<KeyRelease>', self._on_json_changed)
        
        # Notify main window
        self.main_window.set_current_json(json_str)
    
    def _parse_json(self):
        """Parse JSON from text widget and update path."""
        try:
            json_str = self.json_text.get(1.0, tk.END).strip()
            if not json_str:
                return
            
            data = json.loads(json_str)
            
            # Update speeds
            self.start_speed = data.get("start_speed", 0.0)
            self.end_speed = data.get("end_speed", 0.0)
            self.start_speed_var.set(self.start_speed)
            self.end_speed_var.set(self.end_speed)
            
            # Update beziers
            self.beziers = []
            for segment_data in data.get("segments", []):
                bezier = Bezier.from_json(segment_data)
                self.beziers.append(bezier)
            
            # Update commands
            self.commands = []
            for cmd_data in data.get("commands", []):
                command = Command.from_json(cmd_data)
                self.commands.append(command)
            
            # Update UI
            self.path_canvas.set_beziers(self.beziers)
            self.velocity_controls.set_beziers(self.beziers)
            self.command_editor.set_commands(self.commands)
            
            self._rebuild_motion_profile()
            
        except json.JSONDecodeError as e:
            # Invalid JSON - ignore for now
            pass
        except Exception as e:
            print(f"Error parsing JSON: {e}")
    
    def on_file_changed(self):
        """Called when the current file changes."""
        current_file = self.main_window.get_current_file()
        if current_file and os.path.exists(current_file):
            try:
                with open(current_file, 'r') as f:
                    json_str = f.read()
                
                # Update JSON text
                self.json_text.delete(1.0, tk.END)
                self.json_text.insert(1.0, json_str)
                
                # Parse and load data
                self._parse_json()
                
            except Exception as e:
                print(f"Error loading file: {e}")
        else:
            # No file or file doesn't exist - clear everything
            self._clear_all_data()
    
    def on_file_saved(self):
        """Called when the current file is saved."""
        # Could show a brief "saved" indicator here
        pass
    
    def _clear_all_data(self):
        """Clear all path data."""
        self.beziers = []
        self.commands = []
        self.robots = []
        self.start_speed = DEFAULT_START_SPEED
        self.end_speed = DEFAULT_END_SPEED
        self.current_time = 0.0
        self.total_time = 0.0
        
        # Update UI
        self.start_speed_var.set(self.start_speed)
        self.end_speed_var.set(self.end_speed)
        self.path_canvas.set_beziers(self.beziers)
        self.velocity_controls.set_beziers(self.beziers)
        self.command_editor.set_commands(self.commands)
        self.chart_display.clear_charts()
        
        self.json_text.delete(1.0, tk.END)
        self.json_text.insert(1.0, '{\n  "start_speed": 0,\n  "end_speed": 0,\n  "segments": [],\n  "commands": []\n}')

    def destroy(self):
        """Clean up when destroying the widget."""
        # Stop any running simulations
        self._stop_animation()
        self._stop_robot_simulation()
        super().destroy()
