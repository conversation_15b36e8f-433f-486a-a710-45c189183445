// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.3.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'motion_profile.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These functions are ignored (category: IgnoreBecauseOwnerTyShouldIgnore): `new_2d`

// Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MotionProfile2d>>
abstract class MotionProfile2D implements RustOpaqueInterface, MotionProfile {
  Future<MotionCommand?> get_({required Duration t});

  Future<Duration> getDuration();
}
