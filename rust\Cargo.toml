[package]
name = "rust_lib_al_planner"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib", "staticlib"]

[dependencies]
flutter_rust_bridge = "=2.3.0"
lazy_static = "1.5.0"
motion_profiling = { git = "https://github.com/alexDickhans/motion_profiling.git", default-features = false }
nalgebra = { version = "0.33.0", default-features = false, features = [
    "macros", "alloc"
] }
uom = "0.36.0"
