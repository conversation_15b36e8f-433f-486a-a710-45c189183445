#!/usr/bin/env python3
"""
Debug coordinate conversion by implementing exactly what Dar<PERSON> does.
"""

# Dart code:
# Point.fromOffset(Offset offset, Size size)
#     : x = -offset.dy * fieldWidth / size.width + fieldWidth / 2,
#       y = -offset.dx * fieldWidth / size.height + fieldWidth / 2;
#
# double getXScreen(double width) {
#   return -y * width / fieldWidth + width / 2;
# }
#
# double getYScreen(double height) {
#   return -x * height / fieldWidth + height / 2;
# }

fieldWidth = 3.65

def dart_from_offset(offset_dx, offset_dy, size_width, size_height):
    """Exact implementation of Dart Point.fromOffset"""
    x = -offset_dy * fieldWidth / size_width + fieldWidth / 2
    y = -offset_dx * fieldWidth / size_height + fieldWidth / 2
    return (x, y)

def dart_get_screen(field_x, field_y, width, height):
    """Exact implementation of Dart getXScreen/getYScreen"""
    screen_x = -field_y * width / fieldWidth + width / 2
    screen_y = -field_x * height / fieldWidth + height / 2
    return (screen_x, screen_y)

def test_dart_coordinates():
    """Test the Dart coordinate system"""
    print("Testing Dart coordinate conversion")
    print(f"Field width: {fieldWidth}")
    print()
    
    # Test with typical canvas size
    canvas_width = 800
    canvas_height = 600
    
    print(f"Canvas size: {canvas_width} x {canvas_height}")
    print()
    
    # Test screen to field conversion
    print("Screen -> Field (using Dart fromOffset):")
    screen_tests = [
        ("Top-left", 0, 0),
        ("Top-right", canvas_width, 0),
        ("Bottom-left", 0, canvas_height),
        ("Bottom-right", canvas_width, canvas_height),
        ("Center", canvas_width/2, canvas_height/2),
    ]
    
    for name, sx, sy in screen_tests:
        fx, fy = dart_from_offset(sx, sy, canvas_width, canvas_height)
        print(f"{name:<12} ({sx:3.0f}, {sy:3.0f}) -> ({fx:.2f}, {fy:.2f})")
    
    print()
    
    # Test field to screen conversion
    print("Field -> Screen (using Dart getScreen):")
    field_tests = [
        ("Bottom-left", 0.0, 0.0),
        ("Bottom-right", fieldWidth, 0.0),
        ("Top-left", 0.0, fieldWidth),
        ("Top-right", fieldWidth, fieldWidth),
        ("Center", fieldWidth/2, fieldWidth/2),
        ("Sample start", 0.5, 0.5),
        ("Sample end", 3.2, 3.0),
    ]
    
    for name, fx, fy in field_tests:
        sx, sy = dart_get_screen(fx, fy, canvas_width, canvas_height)
        in_bounds = (0 <= sx <= canvas_width) and (0 <= sy <= canvas_height)
        print(f"{name:<12} ({fx:.1f}, {fy:.1f}) -> ({sx:6.1f}, {sy:6.1f}) {'✓' if in_bounds else '✗'}")
    
    print()
    print("Analysis:")
    print("- Field (0,0) should be bottom-left of the field")
    print("- Field (3.65,3.65) should be top-right of the field")
    print("- Screen (0,0) should be top-left of the canvas")
    print("- Screen (800,600) should be bottom-right of the canvas")

if __name__ == "__main__":
    test_dart_coordinates()
