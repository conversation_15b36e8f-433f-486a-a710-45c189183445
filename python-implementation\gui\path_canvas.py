"""
Interactive canvas for drawing and editing Bezier curves.
"""

import os
import tkinter as tk
from typing import List, Callable

from utils.bezier import Bezier
from utils.point import Point
from utils.robot import RobotPosition
from config.constants import (
    POINT_SIZE, ROBOT_SIZE, COMMAND_DOT_SIZE,
    MAX_SPEED, MAX_ACCEL
)

# Try to import PIL, fall back gracefully if not available
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False


class PathCanvas(tk.Canvas):
    """
    Interactive canvas for path editing with Bezier curves.
    """
    
    def __init__(self, parent, on_path_changed: Callable = None):
        """
        Initialize the path canvas.
        
        Args:
            parent: Parent widget
            on_path_changed: Callback when path is modified
        """
        super().__init__(parent, bg='white')
        
        self.on_path_changed = on_path_changed
        self.beziers: List[Bezier] = []
        self.robots: List[RobotPosition] = []
        self.command_dots: List[float] = []
        self.is_skills_mode = False
        
        # Mouse interaction state
        self.dragging_point = None
        self.dragging_bezier_index = -1
        self.dragging_point_index = -1
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        
        # Background images
        self.match_image = None
        self.skills_image = None
        self._load_background_images()
        
        # Bind mouse events
        self.bind("<Button-1>", self._on_mouse_click)
        self.bind("<B1-Motion>", self._on_mouse_drag)
        self.bind("<ButtonRelease-1>", self._on_mouse_release)
        self.bind("<Double-Button-1>", self._on_double_click)
        self.bind("<Configure>", self._on_resize)

        # Bind keyboard events (need focus)
        self.bind("<Key>", self._on_key_press)
        self.bind("<Delete>", self._on_delete_key)
        self.bind("<BackSpace>", self._on_delete_key)
        self.focus_set()

        # Make canvas focusable
        self.config(highlightthickness=1)
    
    def _load_background_images(self):
        """Load background field images."""
        if not PIL_AVAILABLE:
            print("Warning: PIL not available, background images disabled")
            return

        try:
            # Load match field image
            match_path = "assets/match.png"
            if os.path.exists(match_path):
                self.match_image = Image.open(match_path)

            # Load skills field image
            skills_path = "assets/skills.png"
            if os.path.exists(skills_path):
                self.skills_image = Image.open(skills_path)

        except Exception as e:
            print(f"Warning: Could not load background images: {e}")
    
    def set_skills_mode(self, is_skills: bool):
        """Set whether to show skills or match field."""
        self.is_skills_mode = is_skills
        self.redraw()
    
    def set_beziers(self, beziers: List[Bezier]):
        """Set the list of Bezier curves."""
        self.beziers = beziers.copy()
        self.redraw()
    
    def set_robots(self, robots: List[RobotPosition]):
        """Set the list of robot positions."""
        self.robots = robots.copy()
        self.redraw()
    
    def set_command_dots(self, command_dots: List[float]):
        """Set the list of command dot positions."""
        self.command_dots = command_dots.copy()
        self.redraw()
    
    def get_beziers(self) -> List[Bezier]:
        """Get the current list of Bezier curves."""
        return self.beziers.copy()
    
    def _on_resize(self, event):
        """Handle canvas resize."""
        self.redraw()
    
    def _on_mouse_click(self, event):
        """Handle mouse click."""
        self.focus_set()  # Ensure we can receive keyboard events

        # Check if shift is pressed for deletion (0x1 = Shift, 0x4 = Control)
        if event.state & 0x1:  # Shift key
            self._handle_delete_click(event.x, event.y)
        else:
            self._handle_normal_click(event.x, event.y)

        self.last_mouse_x = event.x
        self.last_mouse_y = event.y
    
    def _on_mouse_drag(self, event):
        """Handle mouse drag."""
        if event.state & 0x1:  # Shift key - continue deleting
            self._handle_delete_drag(event.x, event.y)
        else:
            self._handle_normal_drag(event.x, event.y)
        
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y
    
    def _on_mouse_release(self, event):
        """Handle mouse release."""
        self.dragging_point = None
        self.dragging_bezier_index = -1
        self.dragging_point_index = -1
    
    def _on_double_click(self, event):
        """Handle double click to add new Bezier curve or straight line."""
        # Check if Ctrl is pressed for straight line
        is_straight = bool(event.state & 0x4)  # Ctrl key
        self._add_bezier_at_position(event.x, event.y, is_straight)
    
    def _on_key_press(self, event):
        """Handle key press events."""
        # This would handle keyboard shortcuts if needed
        pass

    def _on_delete_key(self, event):
        """Handle Delete/Backspace key press."""
        # Delete the last bezier segment
        if self.beziers:
            del self.beziers[-1]
            print(f"Deleted last bezier segment (Delete/Backspace key)")
            self.redraw()
            if self.on_path_changed:
                self.on_path_changed()
    
    def _handle_normal_click(self, x: float, y: float):
        """Handle normal mouse click (not shift)."""
        # Find if we clicked on a control point
        for i, bezier in enumerate(self.beziers):
            if not bezier.visible:
                continue
                
            point_index = bezier.get_closest_point_index(x, y, self.winfo_width(), self.winfo_height())
            if point_index is not None:
                self.dragging_bezier_index = i
                self.dragging_point_index = point_index
                return
    
    def _handle_normal_drag(self, x: float, y: float):
        """Handle normal mouse drag."""
        if self.dragging_bezier_index >= 0 and self.dragging_point_index >= 0:
            # Calculate delta
            delta_x = x - self.last_mouse_x
            delta_y = y - self.last_mouse_y
            
            # Move the point
            bezier = self.beziers[self.dragging_bezier_index]
            result = bezier.move_point(
                self.dragging_point_index, 
                delta_x, delta_y, 
                self.winfo_width(), self.winfo_height()
            )
            
            # Handle continuity between segments
            self._handle_continuity(self.dragging_bezier_index, result)
            
            self.redraw()
            if self.on_path_changed:
                self.on_path_changed()
    
    def _handle_delete_click(self, x: float, y: float):
        """Handle shift+click for deletion."""
        print(f"Delete click at ({x}, {y})")  # Debug output
        self._delete_bezier_at_position(x, y)
    
    def _handle_delete_drag(self, x: float, y: float):
        """Handle shift+drag for deletion."""
        self._delete_bezier_at_position(x, y)
    
    def _delete_bezier_at_position(self, x: float, y: float):
        """Delete bezier curve at the given position."""
        to_remove = []
        for i, bezier in enumerate(self.beziers):
            if bezier.is_point_over(x, y, self.winfo_width(), self.winfo_height()):
                to_remove.append(i)
                print(f"Found bezier {i} to delete")  # Debug output

        # Remove in reverse order to maintain indices
        for i in reversed(to_remove):
            del self.beziers[i]
            print(f"Deleted bezier {i}")  # Debug output

        if to_remove:
            self.redraw()
            if self.on_path_changed:
                self.on_path_changed()
        else:
            print("No beziers found to delete at this position")  # Debug output
    
    def _add_bezier_at_position(self, x: float, y: float, is_straight_line: bool = False):
        """Add a new Bezier curve or straight line at the given position."""
        end_point = Point.from_screen(x, y, self.winfo_width(), self.winfo_height())

        if not self.beziers:
            # First segment - start near center of field
            start_point = Point(1.8, 1.8)  # Center of field
            if is_straight_line:
                # For straight line, control points will be auto-calculated
                control1 = start_point
                control2 = end_point
            else:
                control1 = start_point.plus(Point(0.3, 0.0))  # Slight offset
                control2 = end_point.minus(Point(0.3, 0.0))   # Approach end point
        else:
            # Continue from last segment
            last_bezier = self.beziers[-1]
            start_point = last_bezier.p4

            if is_straight_line:
                # For straight line, control points will be auto-calculated
                control1 = start_point
                control2 = end_point
            else:
                # Calculate control point for smooth continuation
                direction = last_bezier.p3.minus(last_bezier.p4).times(-2.0)
                control1 = last_bezier.p3.plus(direction)
                control2 = end_point.midpoint(control1)

        new_bezier = Bezier(start_point, control1, control2, end_point,
                           MAX_SPEED, MAX_ACCEL, False, is_straight_line)
        self.beziers.append(new_bezier)

        self.redraw()
        if self.on_path_changed:
            self.on_path_changed()
    
    def _handle_continuity(self, bezier_index: int, moved_point_type: int):
        """Handle continuity between connected Bezier segments."""
        if moved_point_type == 2:  # Moved p2 (first control point)
            # Update previous segment's p3 for continuity
            if bezier_index > 0:
                current = self.beziers[bezier_index]
                previous = self.beziers[bezier_index - 1]
                
                mag = previous.p4.minus(previous.p3).magnitude()
                direction = current.p1.minus(current.p2).norm()
                
                # Check if segments are reversed relative to each other
                reversed_factor = -1.0 if (previous.reversed != current.reversed) else 1.0
                unit = direction.times(reversed_factor)
                
                previous.p3 = previous.p4.plus(unit.times(mag))
        
        elif moved_point_type == 3:  # Moved p3 (second control point)
            # Update next segment's p2 for continuity
            if bezier_index < len(self.beziers) - 1:
                current = self.beziers[bezier_index]
                next_bezier = self.beziers[bezier_index + 1]
                
                mag = next_bezier.p1.minus(next_bezier.p2).magnitude()
                direction = current.p4.minus(current.p3).norm()
                
                # Check if segments are reversed relative to each other
                reversed_factor = -1.0 if (next_bezier.reversed != current.reversed) else 1.0
                unit = direction.times(reversed_factor)
                
                next_bezier.p2 = next_bezier.p1.plus(unit.times(mag))
    
    def flip_all_beziers(self):
        """Flip all Bezier curves across the x-axis."""
        for bezier in self.beziers:
            bezier.flip()
        self.redraw()
        if self.on_path_changed:
            self.on_path_changed()
    
    def redraw(self):
        """Redraw the entire canvas."""
        self.delete("all")
        
        canvas_width = self.winfo_width()
        canvas_height = self.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            return
        
        # Draw background image
        self._draw_background(canvas_width, canvas_height)
        
        # Draw Bezier curves
        self._draw_beziers(canvas_width, canvas_height)
        
        # Draw robots
        self._draw_robots(canvas_width, canvas_height)
        
        # Draw command dots
        self._draw_command_dots(canvas_width, canvas_height)
    
    def _draw_background(self, canvas_width: int, canvas_height: int):
        """Draw the background field image."""
        if PIL_AVAILABLE:
            try:
                image = self.skills_image if self.is_skills_mode else self.match_image
                if image:
                    # Resize image to fit canvas
                    resized = image.resize((canvas_width, canvas_height), Image.Resampling.LANCZOS)
                    self.bg_photo = ImageTk.PhotoImage(resized)
                    self.create_image(0, 0, anchor=tk.NW, image=self.bg_photo)
                    return
            except Exception:
                pass

        # Fallback to solid color
        self.create_rectangle(0, 0, canvas_width, canvas_height,
                            fill='white', outline='')
    
    def _draw_beziers(self, canvas_width: int, canvas_height: int):
        """Draw all Bezier curves and straight lines."""
        for bezier in self.beziers:
            if not (bezier.visible or bezier.focused):
                continue

            # Choose color
            if bezier.focused:
                color = '#32CD32'  # Lime green
            elif bezier.is_straight_line:
                color = '#FF00FF'  # Magenta for straight lines
            elif bezier.reversed:
                color = '#FFA500'  # Orange
            else:
                color = '#0000FF'  # Blue

            # Draw the curve or line
            if bezier.is_straight_line:
                self._draw_straight_line(bezier, canvas_width, canvas_height, color)
            else:
                self._draw_bezier_curve(bezier, canvas_width, canvas_height, color)

            # Draw control points
            self._draw_control_points(bezier, canvas_width, canvas_height)
    
    def _draw_bezier_curve(self, bezier: Bezier, canvas_width: int, canvas_height: int, color: str):
        """Draw a single Bezier curve."""
        points = bezier.get_path_points(50)
        coords = []
        
        for point in points:
            x, y = point.get_screen_coords(canvas_width, canvas_height)
            coords.extend([x, y])
        
        if len(coords) >= 4:
            self.create_line(coords, fill=color, width=3, smooth=True, capstyle=tk.ROUND)

    def _draw_straight_line(self, bezier: Bezier, canvas_width: int, canvas_height: int, color: str):
        """Draw a straight line segment."""
        start_x, start_y = bezier.p1.get_screen_coords(canvas_width, canvas_height)
        end_x, end_y = bezier.p4.get_screen_coords(canvas_width, canvas_height)

        # Draw straight line with dashed pattern to distinguish from curves
        self.create_line(start_x, start_y, end_x, end_y,
                        fill=color, width=4, capstyle=tk.ROUND, dash=(8, 4))
    
    def _draw_control_points(self, bezier: Bezier, canvas_width: int, canvas_height: int):
        """Draw control points for a Bezier curve or straight line with distinct colors."""
        points_screen = bezier.get_control_points_screen(canvas_width, canvas_height)

        # Point 1: Start point (large, filled green circle)
        x1, y1 = points_screen[0]
        self.create_oval(x1-POINT_SIZE, y1-POINT_SIZE, x1+POINT_SIZE, y1+POINT_SIZE,
                        outline='#00AA00', width=3, fill='#88FF88')  # Bright green with light fill

        if bezier.is_straight_line:
            # For straight lines, only show start and end points
            # Point 4: End point (large circle, color depends on stop_end)
            x4, y4 = points_screen[3]
            if bezier.stop_end:
                # Stop end: filled red circle
                self.create_oval(x4-POINT_SIZE, y4-POINT_SIZE, x4+POINT_SIZE, y4+POINT_SIZE,
                               outline='#CC0000', width=3, fill='#FF6666')  # Red with light fill
            else:
                # Continue: orange circle with hollow center
                self.create_oval(x4-POINT_SIZE, y4-POINT_SIZE, x4+POINT_SIZE, y4+POINT_SIZE,
                               outline='#FF6600', width=3, fill='#FFCC88')  # Orange with light fill

            # Draw straight line indicator (thin line between start and end)
            self.create_line(x1, y1, x4, y4, fill='#CCCCCC', width=1, dash=(2, 2))
        else:
            # For Bezier curves, show all control points
            # Point 2: First control point (medium, blue square)
            x2, y2 = points_screen[1]
            self.create_rectangle(x2-POINT_SIZE+2, y2-POINT_SIZE+2, x2+POINT_SIZE-2, y2+POINT_SIZE-2,
                                 outline='#0066FF', width=3, fill='#AACCFF')  # Blue square with light fill

            # Point 3: Second control point (medium, purple diamond)
            x3, y3 = points_screen[2]
            # Draw diamond shape
            diamond_points = [
                x3, y3-POINT_SIZE+2,  # top
                x3+POINT_SIZE-2, y3,  # right
                x3, y3+POINT_SIZE-2,  # bottom
                x3-POINT_SIZE+2, y3   # left
            ]
            self.create_polygon(diamond_points, outline='#AA00AA', width=3, fill='#FFAAFF')  # Purple diamond

            # Point 4: End point (large circle, color depends on stop_end)
            x4, y4 = points_screen[3]
            if bezier.stop_end:
                # Stop end: filled red circle
                self.create_oval(x4-POINT_SIZE, y4-POINT_SIZE, x4+POINT_SIZE, y4+POINT_SIZE,
                               outline='#CC0000', width=3, fill='#FF6666')  # Red with light fill
            else:
                # Continue: orange circle with hollow center
                self.create_oval(x4-POINT_SIZE, y4-POINT_SIZE, x4+POINT_SIZE, y4+POINT_SIZE,
                               outline='#FF6600', width=3, fill='#FFCC88')  # Orange with light fill

            # Draw connection lines between control points (light gray, dashed)
            self.create_line(x1, y1, x2, y2, fill='#CCCCCC', width=1, dash=(3, 3))  # Start to control1
            self.create_line(x3, y3, x4, y4, fill='#CCCCCC', width=1, dash=(3, 3))  # Control2 to end
    
    def _draw_robots(self, canvas_width: int, canvas_height: int):
        """Draw robot positions."""
        for robot in self.robots:
            x, y = robot.get_screen_position(canvas_width, canvas_height)
            
            # Draw robot body
            self.create_oval(x-ROBOT_SIZE, y-ROBOT_SIZE, x+ROBOT_SIZE, y+ROBOT_SIZE,
                           fill='black', outline='black')
            
            # Draw heading indicator
            end_x, end_y = robot.get_heading_end_point(canvas_width, canvas_height, 30.0)
            self.create_line(x, y, end_x, end_y, fill='black', width=3)
    
    def _draw_command_dots(self, canvas_width: int, canvas_height: int):
        """Draw command timing dots."""
        for command_t in self.command_dots:
            if 0 <= command_t < len(self.beziers):
                bezier_index = int(command_t)
                local_t = command_t - bezier_index
                
                if bezier_index < len(self.beziers) and self.beziers[bezier_index].visible:
                    point = self.beziers[bezier_index].evaluate(local_t)
                    x, y = point.get_screen_coords(canvas_width, canvas_height)
                    
                    self.create_oval(x-COMMAND_DOT_SIZE, y-COMMAND_DOT_SIZE, 
                                   x+COMMAND_DOT_SIZE, y+COMMAND_DOT_SIZE,
                                   fill='yellow', outline='yellow')
