"""
Robot position and pose representation.
"""

import math
from typing import Tu<PERSON>, Dict, Any
from config.constants import FIELD_WIDTH


class RobotPosition:
    """
    Represents a robot's position and orientation on the field.
    """
    
    def __init__(self, x: float, y: float, angle: float):
        """
        Initialize robot position.
        
        Args:
            x: X coordinate in field units
            y: Y coordinate in field units  
            angle: Angle in radians
        """
        self.x = x
        self.y = y
        self.angle = angle
    
    def get_screen_position(self, canvas_width: float, canvas_height: float) -> Tuple[float, float]:
        """
        Get robot position in screen coordinates.

        Returns:
            Tuple of (screen_x, screen_y)
        """
        # Dart: Offset(size.width / 2 - y * size.height / fieldWidth,
        #              size.height / 2 - x * size.height / fieldWidth);
        screen_x = canvas_width / 2 - self.y * canvas_height / FIELD_WIDTH
        screen_y = canvas_height / 2 - self.x * canvas_height / FIELD_WIDTH
        return (screen_x, screen_y)
    
    def get_heading_end_point(self, canvas_width: float, canvas_height: float, length: float = 30.0) -> <PERSON><PERSON>[float, float]:
        """
        Get the end point of the robot's heading indicator line.
        
        Args:
            canvas_width: Canvas width in pixels
            canvas_height: Canvas height in pixels
            length: Length of the heading line in pixels
            
        Returns:
            Tuple of (end_x, end_y) screen coordinates
        """
        screen_x, screen_y = self.get_screen_position(canvas_width, canvas_height)
        
        # Calculate end point of heading line
        end_x = screen_x + math.cos(-self.angle - math.pi / 2) * length
        end_y = screen_y + math.sin(-self.angle - math.pi / 2) * length
        
        return (end_x, end_y)
    
    def to_json(self) -> Dict[str, Any]:
        """Convert to JSON-serializable dictionary."""
        return {
            'x': round(self.x, 3),
            'y': round(self.y, 3),
            'angle': round(self.angle, 3)
        }
    
    @classmethod
    def from_json(cls, json_data: Dict[str, Any]) -> 'RobotPosition':
        """Create RobotPosition from JSON data."""
        return cls(json_data['x'], json_data['y'], json_data['angle'])
    
    def copy(self) -> 'RobotPosition':
        """Create a copy of this robot position."""
        return RobotPosition(self.x, self.y, self.angle)
    
    def __str__(self) -> str:
        """String representation."""
        return f"Robot(x={self.x:.2f}, y={self.y:.2f}, angle={math.degrees(self.angle):.1f}°)"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"RobotPosition({self.x}, {self.y}, {self.angle})"


class Pose:
    """
    Alternative pose representation compatible with motion profiling.
    """
    
    def __init__(self, x: float, y: float, theta: float):
        """
        Initialize pose.
        
        Args:
            x: X coordinate
            y: Y coordinate
            theta: Orientation angle in radians
        """
        self.x = x
        self.y = y
        self.theta = theta
    
    def to_robot_position(self) -> RobotPosition:
        """Convert to RobotPosition."""
        return RobotPosition(self.x, self.y, self.theta)
    
    @classmethod
    def from_robot_position(cls, robot_pos: RobotPosition) -> 'Pose':
        """Create Pose from RobotPosition."""
        return cls(robot_pos.x, robot_pos.y, robot_pos.angle)
    
    def __str__(self) -> str:
        """String representation."""
        return f"Pose(x={self.x:.2f}, y={self.y:.2f}, theta={math.degrees(self.theta):.1f}°)"
