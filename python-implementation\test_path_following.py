#!/usr/bin/env python3
"""
Test script to verify that robot position follows the actual path.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.point import Point
from utils.bezier import Bezier
from motion_profiling.simple_api import create_path_from_beziers, get_duration, get_pose
from config.constants import MAX_SPEED, MAX_ACCEL

def test_path_following():
    """Test that robot position follows the Bezier path."""
    print("Testing robot path following...")
    
    # Create a simple test path
    bezier = Bezier(
        Point(1.0, 1.0),  # Start
        Point(1.5, 1.0),  # Control 1
        Point(2.0, 2.0),  # Control 2
        Point(2.5, 2.0),  # End
        MAX_SPEED, MAX_ACCEL
    )
    
    beziers = [bezier]
    
    # Create path and get duration
    path = create_path_from_beziers(beziers, 0.0, 0.0)
    duration_ms = get_duration(path)
    duration_s = duration_ms / 1000.0
    
    print(f"Path duration: {duration_s:.2f} seconds")
    print(f"Bezier start: ({bezier.p1.x:.2f}, {bezier.p1.y:.2f})")
    print(f"Bezier end: ({bezier.p4.x:.2f}, {bezier.p4.y:.2f})")
    print()
    
    # Test robot positions at various times
    print("Time (s) | Robot Position | Expected Position | Distance Error")
    print("-" * 65)
    
    test_times = [0.0, 0.25, 0.5, 0.75, 1.0]
    
    for time_fraction in test_times:
        t = time_fraction * duration_s
        
        # Get robot pose from motion profiling
        robot_pose = get_pose(t)
        
        # Get expected position directly from Bezier curve
        expected_pos = bezier.evaluate(time_fraction)
        
        # Calculate error
        error = ((robot_pose.x - expected_pos.x) ** 2 + (robot_pose.y - expected_pos.y) ** 2) ** 0.5
        
        print(f"{t:8.2f} | ({robot_pose.x:.2f}, {robot_pose.y:.2f}) | ({expected_pos.x:.2f}, {expected_pos.y:.2f}) | {error:.3f}")
    
    print()
    
    # Test with multiple segments
    print("Testing multi-segment path...")
    
    bezier2 = Bezier(
        Point(2.5, 2.0),  # Continue from first
        Point(3.0, 2.0),  # Control 1
        Point(3.0, 1.5),  # Control 2
        Point(3.0, 1.0),  # End
        MAX_SPEED, MAX_ACCEL
    )
    
    multi_beziers = [bezier, bezier2]
    multi_path = create_path_from_beziers(multi_beziers, 0.0, 0.0)
    multi_duration_ms = get_duration(multi_path)
    multi_duration_s = multi_duration_ms / 1000.0
    
    print(f"Multi-segment duration: {multi_duration_s:.2f} seconds")
    
    # Test positions at segment boundaries
    mid_time = multi_duration_s / 2
    end_time = multi_duration_s
    
    mid_pose = get_pose(mid_time)
    end_pose = get_pose(end_time)
    
    print(f"Mid-path position: ({mid_pose.x:.2f}, {mid_pose.y:.2f})")
    print(f"End position: ({end_pose.x:.2f}, {end_pose.y:.2f})")
    print(f"Expected end: ({bezier2.p4.x:.2f}, {bezier2.p4.y:.2f})")
    
    end_error = ((end_pose.x - bezier2.p4.x) ** 2 + (end_pose.y - bezier2.p4.y) ** 2) ** 0.5
    print(f"End position error: {end_error:.3f}")
    
    if end_error < 0.1:
        print("✓ Robot follows path correctly!")
    else:
        print("✗ Robot path following has errors")

if __name__ == "__main__":
    test_path_following()
