#!/usr/bin/env python3
"""
Test coordinate system to debug path positioning issues.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.point import Point
from config.constants import FIELD_WIDTH

def test_coordinate_system():
    """Test the coordinate conversion system."""
    print("Testing coordinate system...")
    print(f"Field width: {FIELD_WIDTH}m")
    print()
    
    # Test canvas size (typical)
    canvas_width = 800
    canvas_height = 600
    
    print(f"Canvas size: {canvas_width} x {canvas_height}")
    print()
    
    # Test key field positions
    test_points = [
        ("Bottom-left corner", Point(0.0, 0.0)),
        ("Bottom-right corner", Point(FIELD_WIDTH, 0.0)),
        ("Top-left corner", Point(0.0, FIELD_WIDTH)),
        ("Top-right corner", Point(FIELD_WIDTH, FIELD_WIDTH)),
        ("Center", Point(FIELD_WIDTH/2, FIELD_WIDTH/2)),
        ("Sample start point", Point(0.5, 0.5)),
        ("Sample mid point", Point(1.5, 1.0)),
        ("Sample end point", Point(3.2, 3.0)),
    ]
    
    print("Field coordinates -> Screen coordinates:")
    print(f"{'Description':<20} {'Field (x,y)':<15} {'Screen (x,y)':<15} {'In bounds?'}")
    print("-" * 70)
    
    for desc, point in test_points:
        screen_x, screen_y = point.get_screen_coords(canvas_width, canvas_height)
        in_bounds = (0 <= screen_x <= canvas_width) and (0 <= screen_y <= canvas_height)
        print(f"{desc:<20} ({point.x:.1f}, {point.y:.1f})      ({screen_x:.1f}, {screen_y:.1f})      {in_bounds}")
    
    print()
    print("Screen coordinates -> Field coordinates:")
    print(f"{'Description':<20} {'Screen (x,y)':<15} {'Field (x,y)':<15}")
    print("-" * 55)
    
    # Test screen positions
    screen_tests = [
        ("Top-left screen", 0, 0),
        ("Top-right screen", canvas_width, 0),
        ("Bottom-left screen", 0, canvas_height),
        ("Bottom-right screen", canvas_width, canvas_height),
        ("Center screen", canvas_width/2, canvas_height/2),
    ]
    
    for desc, sx, sy in screen_tests:
        field_point = Point.from_screen(sx, sy, canvas_width, canvas_height)
        print(f"{desc:<20} ({sx:.0f}, {sy:.0f})        ({field_point.x:.2f}, {field_point.y:.2f})")
    
    print()
    print("Analysis:")
    print(f"- Field coordinates should range from 0 to {FIELD_WIDTH:.2f}")
    print(f"- Screen coordinates should range from 0 to {canvas_width} x {canvas_height}")
    print(f"- Points outside 0-{FIELD_WIDTH:.2f} range will appear outside the field image")
    print()
    print("Expected mapping (based on Dart code):")
    print("- Field (0, 0) should map to screen (400, 600) - bottom center")
    print("- Field (3.65, 0) should map to screen (400, 0) - top center")
    print("- Field (0, 3.65) should map to screen (0, 600) - bottom left")
    print("- Field (3.65, 3.65) should map to screen (0, 0) - top left")
    print("- Field (1.825, 1.825) should map to screen (200, 300) - center")

if __name__ == "__main__":
    test_coordinate_system()
