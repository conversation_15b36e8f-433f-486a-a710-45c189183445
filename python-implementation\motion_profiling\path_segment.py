"""
Path segment and constraints for motion profiling.
"""

from typing import List, Dict, Any
from utils.point import Point


class Constraints:
    """
    Motion constraints for velocity and acceleration.
    """
    
    def __init__(self, velocity: float, accel: float):
        """
        Initialize constraints.
        
        Args:
            velocity: Maximum velocity in m/s
            accel: Maximum acceleration in m/s²
        """
        self.velocity = velocity
        self.accel = accel
    
    def to_json(self) -> Dict[str, Any]:
        """Convert to JSON."""
        return {
            'velocity': self.velocity,
            'accel': self.accel
        }
    
    @classmethod
    def from_json(cls, json_data: Dict[str, Any]) -> 'Constraints':
        """Create from JSON."""
        return cls(json_data['velocity'], json_data['accel'])
    
    def __str__(self) -> str:
        return f"Constraints(v={self.velocity:.2f}, a={self.accel:.2f})"


class PathSegment:
    """
    A path segment with Bezier curve and motion constraints.
    """
    
    def __init__(self, inverted: bool, stop_end: bool, path: List[Point], constraints: Constraints):
        """
        Initialize path segment.
        
        Args:
            inverted: Whether the robot should drive backwards
            stop_end: Whether to stop at the end of this segment
            path: List of 4 control points for Bezier curve
            constraints: Motion constraints
        """
        self.inverted = inverted
        self.stop_end = stop_end
        self.path = path
        self.constraints = constraints
    
    def to_json(self) -> Dict[str, Any]:
        """Convert to JSON."""
        return {
            'inverted': self.inverted,
            'stop_end': self.stop_end,
            'path': [p.to_json() for p in self.path],
            'constraints': self.constraints.to_json()
        }
    
    @classmethod
    def from_json(cls, json_data: Dict[str, Any]) -> 'PathSegment':
        """Create from JSON."""
        path = [Point.from_json(p) for p in json_data['path']]
        constraints = Constraints.from_json(json_data['constraints'])
        return cls(json_data['inverted'], json_data['stop_end'], path, constraints)
    
    def get_start_point(self) -> Point:
        """Get the start point of the segment."""
        return self.path[0]
    
    def get_end_point(self) -> Point:
        """Get the end point of the segment."""
        return self.path[3]
    
    def evaluate(self, t: float) -> Point:
        """
        Evaluate the Bezier curve at parameter t.
        
        Args:
            t: Parameter from 0 to 1
            
        Returns:
            Point on the curve
        """
        p1, p2, p3, p4 = self.path
        
        # De Casteljau's algorithm
        p5 = p1.lerp(p2, t)
        p6 = p2.lerp(p3, t)
        p7 = p3.lerp(p4, t)
        p8 = p5.lerp(p6, t)
        p9 = p6.lerp(p7, t)
        return p8.lerp(p9, t)
    
    def get_derivative(self, t: float) -> Point:
        """
        Get the first derivative (tangent) at parameter t.
        
        Args:
            t: Parameter from 0 to 1
            
        Returns:
            Tangent vector at t
        """
        p1, p2, p3, p4 = self.path
        
        # First derivative of cubic Bezier
        # B'(t) = 3(1-t)²(P2-P1) + 6(1-t)t(P3-P2) + 3t²(P4-P3)
        one_minus_t = 1.0 - t
        
        term1 = p2.minus(p1).times(3 * one_minus_t * one_minus_t)
        term2 = p3.minus(p2).times(6 * one_minus_t * t)
        term3 = p4.minus(p3).times(3 * t * t)
        
        return term1.plus(term2).plus(term3)
    
    def get_length_estimate(self, num_segments: int = 100) -> float:
        """
        Estimate the length of the path segment.
        
        Args:
            num_segments: Number of segments for approximation
            
        Returns:
            Estimated length in field units
        """
        total_length = 0.0
        prev_point = self.evaluate(0.0)
        
        for i in range(1, num_segments + 1):
            t = i / num_segments
            current_point = self.evaluate(t)
            total_length += prev_point.distance_to(current_point)
            prev_point = current_point
        
        return total_length
    
    def __str__(self) -> str:
        return f"PathSegment(inverted={self.inverted}, stop_end={self.stop_end}, constraints={self.constraints})"
