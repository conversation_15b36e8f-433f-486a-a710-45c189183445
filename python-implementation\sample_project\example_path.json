{"start_speed": 0, "end_speed": 0, "segments": [{"inverted": false, "stop_end": false, "path": [{"x": 0.5, "y": 0.5}, {"x": 0.8, "y": 0.5}, {"x": 1.2, "y": 0.8}, {"x": 1.5, "y": 1.0}], "constraints": {"velocity": 1.0, "accel": 2.0}}, {"inverted": false, "stop_end": false, "path": [{"x": 1.5, "y": 1.0}, {"x": 1.8, "y": 1.2}, {"x": 2.2, "y": 1.5}, {"x": 2.5, "y": 1.8}], "constraints": {"velocity": 1.2, "accel": 2.5}}, {"inverted": false, "stop_end": true, "path": [{"x": 2.5, "y": 1.8}, {"x": 2.8, "y": 2.0}, {"x": 3.0, "y": 2.5}, {"x": 3.2, "y": 3.0}], "constraints": {"velocity": 0.8, "accel": 1.5}}], "commands": [{"t": 0.5, "name": "start_intake"}, {"t": 1.8, "name": "deploy_arm"}, {"t": 2.5, "name": "stop_intake"}]}